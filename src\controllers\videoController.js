const videoProcessor = require('../services/videoProcessor');
const speechToText = require('../services/speechToText');
const visionAnalysis = require('../services/visionAnalysis');
const geminiService = require('../services/geminiService');
const contentValidator = require('../services/contentValidator');
const cvTemplate = require('../templates/cvTemplate');
const pdfGenerator = require('../services/pdfGenerator');
const audioEnhancement = require('../services/audioEnhancementService');
const fs = require('fs');
const path = require('path');

// In-memory progress tracking store
const progressStore = new Map();

class VideoController {

    // Update progress for a specific request
    updateProgress(sessionId, message, progress) {
        progressStore.set(sessionId, {
            message,
            progress,
            timestamp: Date.now()
        });
    }

    // Get progress for a specific request
    getProgress(sessionId) {
        return progressStore.get(sessionId) || { message: 'Initializing...', progress: 0 };
    }

    // Clear progress for a specific request
    clearProgress(sessionId) {
        progressStore.delete(sessionId);
    }

    async processVideo(req, res) {
        const sessionId = req.body.sessionId || req.file?.filename || `session_${Date.now()}`;
        let tempFiles = []; // Track temporary files for cleanup

        try {
            console.log('🎬 Starting video processing...');
            console.log('📁 File received:', req.file?.path);
            console.log('📊 File details:', {
                originalname: req.file?.originalname,
                mimetype: req.file?.mimetype,
                size: req.file?.size
            });

            // Step 1: Validate video file
            this.updateProgress(sessionId, 'Validating video file...', 5);
            console.log('Validating video file...');
            const metadata = await videoProcessor.getVideoMetadata(req.file.path);
            console.log('Video metadata:', { duration: metadata.format.duration, size: metadata.format.size });
            const validation = videoProcessor.validateVideo(metadata);
            console.log('Validation result:', validation);

            if (!validation.isValid) {
                return res.status(400).json({ error: 'Invalid video file', details: validation.errors });
            }

            // Step 2: Extract audio from video
            this.updateProgress(sessionId, 'Extracting audio from video...', 10);
            console.log('Extracting audio...');
            const audioPath = await videoProcessor.extractAudio(req.file.path);
            tempFiles.push(audioPath);

            // Step 2.5: Enhance audio quality for better transcription (fast mode)
            // Check if enhancement is needed
            const fs = require('fs');
            const audioStats = fs.statSync(audioPath);
            const audioSizeMB = audioStats.size / (1024 * 1024);

            let finalAudioPath = audioPath;
            if (audioSizeMB > 1) {
                this.updateProgress(sessionId, 'Enhancing audio quality...', 15);
                console.log('Enhancing audio quality...');
                const enhancedAudioPath = await audioEnhancement.enhanceAudio(audioPath, true); // Fast mode
                tempFiles.push(enhancedAudioPath);
                finalAudioPath = enhancedAudioPath;
            } else {
                console.log('Skipping audio enhancement for small file...');
            }

            // Log final audio path and size
            const finalAudioStats = fs.statSync(finalAudioPath);
            const finalAudioSizeMB = finalAudioStats.size / (1024 * 1024);
            console.log(`Final audio path: ${finalAudioPath}`);
            console.log(`Final audio size: ${finalAudioSizeMB.toFixed(2)} MB`);

            // Step 3: Transcribe audio to text
            this.updateProgress(sessionId, 'Transcribing speech to text...', 25);
            console.log('Transcribing audio...');
            const { transcription, wordTimings } = await speechToText.transcribeAudio(finalAudioPath);

            // Validate transcription quality
            const minExpectedWords = Math.max(Math.floor(metadata.format.duration / 3), 300); // At least 300 words or 1 word per 3 seconds
            if (transcription.length < 100 || transcription.split(' ').length < Math.min(minExpectedWords, 50)) {
                console.warn(`⚠️  Low transcription quality detected:`);
                console.warn(`   - Video duration: ${metadata.format.duration.toFixed(1)}s`);
                console.warn(`   - Transcription length: ${transcription.length} characters`);
                console.warn(`   - Word count: ${transcription.split(' ').length}`);
                console.warn(`   - Expected minimum words: ${Math.min(minExpectedWords, 50)}`);
            }

            // Step 4: Analyze speech patterns
            this.updateProgress(sessionId, 'Analyzing speech patterns...', 40);
            const speechPatterns = speechToText.analyzeSpeechPatterns(wordTimings);

            // Step 5: Extract key frames from video (reduced for speed)
            this.updateProgress(sessionId, 'Extracting video frames...', 50);
            console.log('Extracting video frames...');
            const framePaths = await videoProcessor.extractKeyFrames(req.file.path, 3); // Reduced from 5 to 3 frames
            tempFiles.push(...framePaths);

            // Step 6: Analyze visual content
            this.updateProgress(sessionId, 'Analyzing visual content...', 65);
            console.log('Analyzing visual content...');
            const visualAnalysis = await visionAnalysis.analyzeFrames(framePaths);

            // Step 7: Extract CV information using Gemini (with fallback)
            this.updateProgress(sessionId, 'Extracting CV information...', 75);
            console.log('Extracting CV information...');
            let extractedCVData;
            try {
                // Add timeout wrapper for Gemini API call
                const geminiPromise = geminiService.extractCVInformation(transcription, visualAnalysis);
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Gemini API timeout')), 30000); // 30 second timeout
                });
                
                extractedCVData = await Promise.race([geminiPromise, timeoutPromise]);
            } catch (error) {
                console.warn('Gemini API failed or timed out, using fallback CV extraction:', error.message);
                // Fallback: Create basic CV structure from transcription
                extractedCVData = this.createFallbackCVData(transcription, visualAnalysis);
            }

            // Step 8: Validate and sanitize CV data
            this.updateProgress(sessionId, 'Validating CV data...', 80);
            const validationResult = contentValidator.validateCVData(extractedCVData);
            if (!validationResult.isValid) {
                console.warn('CV data validation warnings:', validationResult.warnings);
            }
            const sanitizedCVData = contentValidator.sanitizeCVData(extractedCVData);

            // Step 9: Skip CV enhancement for faster processing
            this.updateProgress(sessionId, 'Preparing CV content...', 85);
            console.log('Skipping CV enhancement for faster processing...');
            const enhancedCVData = sanitizedCVData; // Use sanitized data directly

            // Step 10: Generate HTML CV
            this.updateProgress(sessionId, 'Generating professional CV...', 90);
            console.log('Generating CV template...');
            const cvHTML = cvTemplate.generateModernCV(enhancedCVData);

            // Step 11: Generate PDF
            this.updateProgress(sessionId, 'Creating PDF document...', 95);
            const pdfFileName = `cv_${Date.now()}`;
            console.log('Generating PDF...');
            const pdfResult = await pdfGenerator.generatePDF(cvHTML, pdfFileName);

            // Step 12: Cleanup temporary files
            this.updateProgress(sessionId, 'Finalizing your CV...', 100);
            await this.cleanup(tempFiles);

            // Clear progress
            this.clearProgress(sessionId);

            // Step 13: Prepare response with comprehensive results
            const response = {
                success: true,
                sessionId: sessionId,
                cvData: enhancedCVData,
                cv_html: cvHTML,
                pdf_url: pdfResult.url,
                transcription: transcription, // Include full transcription text
                analysis_summary: this.generateAnalysisSummary(transcription, visualAnalysis, speechPatterns, metadata),
                processing_stats: {
                    videoDuration: metadata.format.duration,
                    transcriptionLength: transcription.length,
                    framesAnalyzed: framePaths.length,
                    processingTime: Date.now() - req.startTime
                }
            };

            console.log('Video processing completed successfully');
            res.json(response);

        } catch (error) {
            console.error('Video processing error:', error);
            await this.cleanup(tempFiles);
            this.clearProgress(sessionId); // Clear progress on error
            res.status(500).json({ error: 'Video processing failed', details: error.message });
        }
    }

    generateAnalysisSummary(transcription, visualAnalysis, speechPatterns, metadata) {
        const wordCount = transcription.split(' ').length;
        const minExpectedWords = Math.max(Math.floor(metadata.format.duration / 3), 300);
        const qualityWarning = (transcription.length < 1000 || wordCount < minExpectedWords)
            ? 'Low transcription quality detected. CV generation may be limited.'
            : null;

        const summary = {
            transcription: {
                length: transcription.length,
                wordCount: wordCount,
                fluency: speechPatterns.speechFluency,
                qualityWarning: qualityWarning,
                expectedWords: minExpectedWords,
                actualWords: wordCount
            },
            visualAnalysis: {
                professionalScore: visualAnalysis.overallProfessionalScore,
                eyeContact: visualAnalysis.summary.presentationQuality.eyeContact,
                background: visualAnalysis.summary.presentationQuality.background,
                enthusiasm: visualAnalysis.summary.presentationQuality.enthusiasm
            },
            speechAnalysis: {
                wordsPerMinute: speechPatterns.wordsPerMinute,
                averagePause: speechPatterns.averagePause,
                longestPause: speechPatterns.longestPause
            },
            videoMetadata: {
                duration: metadata.format.duration,
                size: metadata.format.size
            }
        };
        return summary;
    }

    generateRecommendations(transcription, visualAnalysis, speechPatterns) {
        const recommendations = [];

        if (speechPatterns.wordsPerMinute < 120) {
            recommendations.push('Consider speaking more quickly to maintain audience engagement');
        } else if (speechPatterns.wordsPerMinute > 200) {
            recommendations.push('Try speaking more slowly for better clarity');
        }

        if (visualAnalysis.summary.presentationQuality.eyeContact.score < 60) {
            recommendations.push('Work on maintaining better eye contact with the camera');
        }

        if (visualAnalysis.summary.presentationQuality.background.score < 60) {
            recommendations.push('Consider presenting in a more professional setting');
        }

        if (speechPatterns.averagePause > 1.0) {
            recommendations.push('Reduce pauses to improve speech fluency');
        }

        return recommendations;
    }

    async cleanup(filePaths) {
        for (const filePath of filePaths) {
            try {
                if (fs.existsSync(filePath)) {
                    if (fs.statSync(filePath).isDirectory()) {
                        fs.rmdirSync(filePath, { recursive: true });
                    } else {
                        fs.unlinkSync(filePath);
                    }
                }
            } catch (error) {
                console.warn(`Failed to cleanup ${filePath}:`, error);
            }
        }
    }

    async downloadCV(req, res) {
        try {
            const filename = req.params.filename;
            const filePath = path.join(process.env.OUTPUT_DIR || 'output', `${filename}.pdf`);

            if (!fs.existsSync(filePath)) {
                return res.status(404).json({ error: 'File not found' });
            }

            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.pdf"`);

            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);

        } catch (error) {
            console.error('Download error:', error);
            res.status(500).json({ error: 'Download failed' });
        }
    }

    async cleanup(tempFiles) {
        try {
            for (const filePath of tempFiles) {
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                    console.log(`🗑️ Cleaned up temporary file: ${filePath}`);
                }
            }
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    createFallbackCVData(transcription, visualAnalysis) {
        // Extract basic information from transcription using simple pattern matching
        const text = transcription.toLowerCase();

        // Basic CV structure
        return {
            personalInfo: {
                name: "Candidate", // Would need to be provided by user
                email: "", // Would need to be provided by user
                phone: "",
                location: "",
                linkedin: "",
                portfolio: ""
            },
            summary: transcription.length > 100 ? transcription.substring(0, 200) + "..." : transcription,
            experience: [],
            education: [],
            skills: this.extractSkillsFromText(text),
            projects: [],
            certifications: [],
            languages: ["English"], // Default assumption
            interests: []
        };
    }

    extractSkillsFromText(text) {
        // Simple keyword-based skill extraction
        const skillKeywords = [
            'javascript', 'python', 'java', 'react', 'node', 'html', 'css', 'sql',
            'aws', 'docker', 'kubernetes', 'git', 'agile', 'scrum', 'leadership',
            'communication', 'problem solving', 'teamwork', 'project management'
        ];

        const foundSkills = skillKeywords.filter(skill => text.includes(skill));
        return foundSkills.length > 0 ? foundSkills : ['Communication', 'Problem Solving']; // Fallback
    }
}

module.exports = VideoController;