<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Video CV Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .upload-section {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .upload-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .upload-area {
            border: 3px dashed #e1e5e9;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: #e8f0ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .upload-area h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .upload-area p {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            margin-top: 1rem;
            padding: 1rem;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #4caf50;
        }

        .file-info.show {
            display: block;
        }

        .file-info.hide {
            display: none;
        }

        .processing-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .processing-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .processing-modal {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .processing-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .processing-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .processing-message {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            min-height: 2rem;
        }

        .progress-container {
            margin-bottom: 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 17px;
            background: #e1e5e9;
            border-radius: 25px;
            overflow: hidden;
            margin-bottom: 0.5rem;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            animation: shine 2s infinite;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            border-radius: 25px;
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
            animation: breathe 2s ease-in-out infinite;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShine 1.5s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes breathe {
            0%, 100% {
                opacity: 1;
                transform: scaleY(1);
            }
            50% {
                opacity: 0.8;
                transform: scaleY(0.95);
            }
        }

        @keyframes progressShine {
            0%, 100% {
                opacity: 0;
                transform: translateX(-100%);
            }
            50% {
                opacity: 1;
                transform: translateX(0%);
            }
        }

        .progress-text {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .results-section {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: 2rem;
            display: none;
        }

        .results-section.show {
            display: block;
        }

        .cv-preview {
            margin: 2rem 0;
            padding: 2rem;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .download-btn {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 1rem 0;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .analysis-section {
            margin-top: 2rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .analysis-section h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #c62828;
            margin: 1rem 0;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .upload-section {
                padding: 2rem;
            }

            .upload-area {
                padding: 2rem;
            }

            .upload-icon {
                font-size: 3rem;
            }

            .processing-modal {
                padding: 2rem;
                margin: 1rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8rem;
            }

            .upload-section {
                padding: 1.5rem;
            }

            .upload-area {
                padding: 1.5rem;
            }

            .processing-modal {
                padding: 1.5rem;
            }
        }

        .recording-guide {
            text-align: center;
            margin: 1rem 0;
        }

        .guide-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .guide-link:hover {
            color: #0056b3;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎥 AI Video CV Generator</h1>
            <p>Transform your video introduction into a professional CV using advanced AI</p>
        </header>

        <section class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📹</div>
                <h3>Upload Your Video</h3>
                <p>Drag & drop your video file here or click to browse<br><small>Supported formats: MP4, MOV, AVI (Max 500MB)</small></p>
                <input type="file" id="videoFile" class="file-input" accept="video/*">
                <button class="upload-btn" id="processBtn" onclick="event.stopPropagation(); processVideo()">Generate CV</button>
            </div>

            <div class="recording-guide">
                <p>📱 <a href="recording-guide.html" target="_blank" class="guide-link">Recording Tips for Best Results</a></p>
            </div>

            <div class="file-info hide" id="fileInfo">
                <strong>Selected:</strong> <span id="fileName"></span> (<span id="fileSize"></span>)
            </div>
        </section>

        <div class="error-message" id="errorMessage"></div>

        <div class="processing-overlay" id="processingOverlay">
            <div class="processing-modal">
                <div class="processing-icon">⚡</div>
                <h2 class="processing-title">Processing Your Video</h2>
                <p class="processing-message" id="processingMessage">Initializing...</p>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">0% Complete</div>
                </div>
            </div>
        </div>

        <section class="results-section" id="resultsSection">
            <h2 style="text-align: center; margin-bottom: 2rem; color: #333;">🎉 Your CV is Ready!</h2>
            <div class="cv-preview" id="cvPreview"></div>
            <button class="download-btn" id="downloadBtn" onclick="downloadPDF()">📄 Download PDF</button>

            <div class="analysis-section">
                <h3>📊 Analysis Summary</h3>
                <div id="analysisSummary"></div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>