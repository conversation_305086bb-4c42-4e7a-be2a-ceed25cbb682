const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testAPI() {
    console.log('🧪 Starting server and testing API endpoint...\n');

    try {
        // Start the server
        console.log('📡 Starting server...');
        const App = require('../src/app.js');
        const app = new App();
        app.start();

        // Wait for server to start
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('✅ Server started\n');

        // Use the actual test video file
        const testVideoPath = path.join(__dirname, '..', 'video-test.mp4');

        // Check if test video exists
        if (!fs.existsSync(testVideoPath)) {
            throw new Error('Test video file not found: ' + testVideoPath);
        }

        console.log('📹 Using test video:', testVideoPath);

        // Test the API
        console.log('📤 Sending request to /api/process-video...');
        console.log('⏳ This may take several minutes for long videos due to speech recognition processing...');

        const FormData = require('form-data');
        const form = new FormData();
        form.append('video', fs.createReadStream(testVideoPath));

        const response = await axios.post('http://localhost:3001/api/process-video', form, {
            headers: {
                ...form.getHeaders(),
                'Content-Type': 'multipart/form-data'
            },
            timeout: 300000 // 5 minutes for long-running speech recognition
        });

        console.log('✅ API test successful!');
        console.log('Response status:', response.status);
        console.log('CV generated for:', response.data.cvData?.personalInfo?.name);

    } catch (error) {
        console.error('❌ API test failed:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', JSON.stringify(error.response.data, null, 2));
        } else if (error.code) {
            console.error('Error code:', error.code);
        }
        process.exit(1);
    }
}

testAPI();