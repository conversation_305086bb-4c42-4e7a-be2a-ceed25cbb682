const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testTranscriptionAccess() {
    console.log('🧪 Testing transcription access in API response...\n');

    try {
        // Use the actual test video file
        const testVideoPath = path.join(__dirname, 'video-test.mp4');

        // Check if test video exists
        if (!fs.existsSync(testVideoPath)) {
            console.log('❌ Test video file not found:', testVideoPath);
            console.log('Available files in directory:');
            const files = fs.readdirSync(__dirname).filter(f => f.endsWith('.mp4'));
            console.log(files);
            return;
        }

        console.log('📹 Using test video:', testVideoPath);

        // Check video file size
        const stats = fs.statSync(testVideoPath);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`📊 Video file size: ${fileSizeMB} MB`);

        if (parseFloat(fileSizeMB) > 50) {
            console.log('⚠️  Large video file detected. Processing may take several minutes.');
            console.log('💡 Consider using a shorter video for testing.');
        }

        // Test the API with longer timeout
        console.log('📤 Sending request to /api/process-video...');
        console.log('⏳ Processing video (this may take 2-10 minutes depending on video length)...');

        const FormData = require('form-data');
        const form = new FormData();
        form.append('video', fs.createReadStream(testVideoPath));
        form.append('sessionId', 'test-transcription-access-' + Date.now());

        // Increase timeout to 15 minutes for video processing
        const response = await axios.post('http://localhost:3001/api/process-video', form, {
            headers: {
                ...form.getHeaders(),
                'Content-Type': 'multipart/form-data'
            },
            timeout: 900000, // 15 minutes
            onDownloadProgress: (progressEvent) => {
                // This won't show upload progress, but we can show we're waiting
                if (progressEvent.loaded > 0) {
                    console.log('📤 Upload complete, processing video...');
                }
            }
        });

        console.log('\n✅ API test successful!');
        console.log('📊 Response Analysis:');

        // Check if transcription is included
        if (response.data.transcription) {
            console.log('✅ Transcription is accessible!');
            console.log('📝 Transcription length:', response.data.transcription.length, 'characters');
            console.log('📝 First 200 characters:', response.data.transcription.substring(0, 200) + '...');

            // Save transcription to file for debugging
            const debugFile = path.join(__dirname, 'debug-transcription.txt');
            fs.writeFileSync(debugFile, response.data.transcription);
            console.log('💾 Full transcription saved to:', debugFile);

            // Check if it's also in analysis summary
            if (response.data.analysis_summary?.transcription) {
                console.log('✅ Transcription metadata in analysis summary:');
                console.log('   - Length:', response.data.analysis_summary.transcription.length);
                console.log('   - Word count:', response.data.analysis_summary.transcription.wordCount);
                console.log('   - Fluency:', Math.round(response.data.analysis_summary.transcription.fluency) + '%');
            }
        } else {
            console.log('❌ Transcription not found in response');
        }

        console.log('\n📋 Available response fields:');
        Object.keys(response.data).forEach(key => {
            if (key === 'transcription') {
                console.log(`   ✅ ${key}: ${response.data[key].length} characters`);
            } else if (key === 'cv_html') {
                console.log(`   ✅ ${key}: ${response.data[key].length} characters (HTML)`);
            } else if (key === 'analysis_summary') {
                console.log(`   ✅ ${key}: Object with ${Object.keys(response.data[key]).length} properties`);
            } else {
                console.log(`   ✅ ${key}: ${typeof response.data[key]}`);
            }
        });

        // Save full response for debugging
        const responseFile = path.join(__dirname, 'debug-response.json');
        fs.writeFileSync(responseFile, JSON.stringify(response.data, null, 2));
        console.log('💾 Full API response saved to:', responseFile);

    } catch (error) {
        console.log('\n❌ Test failed:', error.message);

        if (error.code === 'ECONNABORTED') {
            console.log('⏰ Request timed out. This is normal for video processing.');
            console.log('💡 Video processing can take 2-10 minutes depending on length.');
            console.log('💡 Try using a shorter video file for testing.');
        } else if (error.response) {
            console.log('📡 Response status:', error.response.status);
            console.log('📄 Response data:', error.response.data);
        } else {
            console.log('🔍 Error details:', error);
        }

        console.log('\n🔧 Troubleshooting tips:');
        console.log('1. Check if server is running: curl http://localhost:3001/api/health');
        console.log('2. Try a shorter video file (< 30 seconds)');
        console.log('3. Check server logs for errors');
        console.log('4. Ensure all API keys are configured in .env');
    }
}

// Run the test
testTranscriptionAccess();