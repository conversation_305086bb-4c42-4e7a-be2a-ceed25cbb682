const ffmpeg = require('@ts-ffmpeg/fluent-ffmpeg');
const path = require('path');
const fs = require('fs');

// Configure FFmpeg to use local binaries
const ffmpegPath = path.join(__dirname, '../../ffmpeg/ffmpeg-8.0-essentials_build/bin/ffmpeg.exe');
const ffprobePath = path.join(__dirname, '../../ffmpeg/ffmpeg-8.0-essentials_build/bin/ffprobe.exe');

if (fs.existsSync(ffmpegPath)) {
    ffmpeg.setFfmpegPath(ffmpegPath);
    console.log('✅ FFmpeg path configured:', ffmpegPath);
} else {
    console.warn('⚠️  FFmpeg not found at:', ffmpegPath);
}

if (fs.existsSync(ffprobePath)) {
    ffmpeg.setFfprobePath(ffprobePath);
    console.log('✅ FFprobe path configured:', ffprobePath);
} else {
    console.warn('⚠️  FFprobe not found at:', ffprobePath);
}

class VideoProcessor {

    // Extract basic metadata from uploaded video
    async getVideoMetadata(videoPath) {
        // Check if we're in test mode
        if (process.env.TEST_MODE === 'true') {
            console.log('🧪 TEST MODE: Using mock video metadata');
            return this.getMockMetadata();
        }

        return new Promise((resolve, reject) => {
            ffmpeg.ffprobe(videoPath, (err, metadata) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(metadata);
                }
            });
        });
    }

    // Validate that video meets our requirements
    validateVideo(metadata) {
        const errors = [];

        // Check duration (allow up to 5 minutes for flexibility)
        if (metadata.format.duration < 15 || metadata.format.duration > 300) {
            errors.push('Video should be between 15 seconds and 5 minutes long');
        }

        // Ensure both audio and video streams exist
        const hasVideo = metadata.streams.some(stream => stream.codec_type === 'video');
        const hasAudio = metadata.streams.some(stream => stream.codec_type === 'audio');

        if (!hasAudio) {
            errors.push('Video must contain audio for speech analysis');
        }

        if (!hasVideo) {
            errors.push('File must contain video stream');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Extract audio from video for speech analysis
    async extractAudio(videoPath) {
        // Check if we're in test mode
        if (process.env.TEST_MODE === 'true') {
            console.log('🧪 TEST MODE: Mocking audio extraction');
            const audioPath = videoPath.replace(path.extname(videoPath), '_audio.wav');
            // Create a dummy audio file for testing
            fs.writeFileSync(audioPath, 'mock audio data');
            return audioPath;
        }

        const audioPath = videoPath.replace(path.extname(videoPath), '_audio.wav');

        return new Promise((resolve, reject) => {
            console.log('🎵 Extracting audio from video...');
            ffmpeg(videoPath)
                .toFormat('wav')
                .audioCodec('pcm_s16le')
                .audioChannels(1)
                .audioFrequency(16000)
                .audioBitrate('256k')  // Explicitly set bitrate
                .on('end', () => {
                    console.log('✅ Audio extraction completed');
                    resolve(audioPath);
                })
                .on('error', (err) => {
                    console.error('❌ Audio extraction failed:', err);
                    reject(err);
                })
                .save(audioPath);
        });
    }

    // Extract key frames from video for visual analysis
    async extractKeyFrames(videoPath, numFrames = 5) {
        // Check if we're in test mode
        if (process.env.TEST_MODE === 'true') {
            console.log('🧪 TEST MODE: Mocking frame extraction');
            const frameDir = path.join(path.dirname(videoPath), 'frames');
            if (!fs.existsSync(frameDir)) {
                fs.mkdirSync(frameDir, { recursive: true });
            }
            // Create dummy frame files
            const frames = [];
            for (let i = 0; i < numFrames; i++) {
                const framePath = path.join(frameDir, `frame-${i}.png`);
                fs.writeFileSync(framePath, 'mock frame data');
                frames.push(framePath);
            }
            return frames;
        }

        const frameDir = path.join(path.dirname(videoPath), 'frames');

        // Create frames directory
        if (!fs.existsSync(frameDir)) {
            fs.mkdirSync(frameDir, { recursive: true });
        }

        return new Promise((resolve, reject) => {
            const frames = [];

            ffmpeg(videoPath)
                .on('filenames', (filenames) => {
                    frames.push(...filenames.map(f => path.join(frameDir, f)));
                })
                .on('end', () => {
                    resolve(frames);
                })
                .on('error', (err) => reject(err))
                .screenshots({
                    count: numFrames,
                    folder: frameDir,
                    filename: 'frame-%i.png',
                    timemarks: Array.from({length: numFrames}, (_, i) => `${(i+1) * 100 / (numFrames+1)}%`)
                });
        });
    }

    getMockMetadata() {
        return {
            format: {
                duration: 32.5,
                size: 15728640, // 15MB
                bit_rate: '128000'
            },
            streams: [
                {
                    codec_type: 'video',
                    codec_name: 'h264',
                    width: 1920,
                    height: 1080,
                    duration: 32.5
                },
                {
                    codec_type: 'audio',
                    codec_name: 'aac',
                    sample_rate: '44100',
                    channels: 2,
                    duration: 32.5
                }
            ]
        };
    }
}

module.exports = new VideoProcessor();