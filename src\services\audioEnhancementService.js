const ffmpeg = require('@ts-ffmpeg/fluent-ffmpeg');
const path = require('path');
const fs = require('fs');

// Configure FFmpeg
const ffmpegPath = path.join(__dirname, '../../ffmpeg/ffmpeg-8.0-essentials_build/bin/ffmpeg.exe');
const ffprobePath = path.join(__dirname, '../../ffmpeg/ffmpeg-8.0-essentials_build/bin/ffprobe.exe');

if (fs.existsSync(ffmpegPath)) {
    ffmpeg.setFfmpegPath(ffmpegPath);
}

if (fs.existsSync(ffprobePath)) {
    ffmpeg.setFfprobePath(ffprobePath);
}

class AudioEnhancementService {
    constructor() {
        this.tempDir = path.join(__dirname, '../../temp');
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    async enhanceAudio(inputPath, fastMode = true) {
        console.log('🎵 Enhancing audio quality for better transcription...');

        const enhancedPath = path.join(this.tempDir, `enhanced-${Date.now()}.wav`);

        return new Promise((resolve, reject) => {
            const ffmpegCommand = ffmpeg(inputPath)
                .toFormat('wav')
                .audioCodec('pcm_s16le')
                .audioChannels(1)
                .audioFrequency(16000)
                .audioBitrate('256k');  // Ensure proper bitrate

            if (fastMode) {
                // Fast mode: minimal enhancement for speed
                ffmpegCommand.audioFilters([
                    'loudnorm=I=-16:TP=-1.5:LRA=11', // Normalize audio levels
                    'highpass=f=80', // Remove low frequency noise
                    'lowpass=f=8000' // Remove high frequency noise
                ]);
            } else {
                // Full enhancement mode
                ffmpegCommand.audioFilters([
                    'loudnorm=I=-16:TP=-1.5:LRA=11',
                    'highpass=f=80',
                    'lowpass=f=8000',
                    'compand=attacks=0.3:decays=0.8:points=-70/-60|-30/-15|0/-5',
                    'deess',
                    'reverb=50:50:50:50:0:0'
                ]);
            }

            ffmpegCommand
                .on('end', () => {
                    console.log('✅ Audio enhancement completed');
                    resolve(enhancedPath);
                })
                .on('error', (err) => {
                    console.warn('⚠️ Audio enhancement failed, using original:', err.message);
                    // Return original file if enhancement fails
                    resolve(inputPath);
                })
                .save(enhancedPath);
        });
    }

    async analyzeAudioQuality(audioPath) {
        return new Promise((resolve, reject) => {
            ffmpeg.ffprobe(audioPath, (err, metadata) => {
                if (err) {
                    reject(err);
                    return;
                }

                const audioStream = metadata.streams.find(s => s.codec_type === 'audio');
                if (!audioStream) {
                    reject(new Error('No audio stream found'));
                    return;
                }

                const quality = {
                    sampleRate: audioStream.sample_rate,
                    channels: audioStream.channels,
                    bitRate: audioStream.bit_rate,
                    duration: metadata.format.duration,
                    size: metadata.format.size,
                    // Estimate quality score
                    qualityScore: this.calculateQualityScore(audioStream, metadata.format)
                };

                resolve(quality);
            });
        });
    }

    calculateQualityScore(audioStream, format) {
        let score = 100;

        // Sample rate penalty
        if (audioStream.sample_rate < 16000) score -= 20;
        else if (audioStream.sample_rate > 48000) score -= 10;

        // Bit rate penalty
        if (audioStream.bit_rate < 64000) score -= 15;

        // Duration penalty (too short)
        if (format.duration < 30) score -= 10;

        // File size penalty (too small for duration)
        const expectedSize = format.duration * 256000 / 8; // 256kbps expected
        const sizeRatio = format.size / expectedSize;
        if (sizeRatio < 0.5) score -= 20;

        return Math.max(0, score);
    }

    async cleanup(filePath) {
        try {
            if (filePath.includes('enhanced-') && fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log('🧹 Cleaned up enhanced audio file');
            }
        } catch (error) {
            console.warn('Failed to cleanup enhanced audio:', error);
        }
    }
}

module.exports = new AudioEnhancementService();