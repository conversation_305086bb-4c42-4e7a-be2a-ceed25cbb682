const axios = require('axios');
const speech = require('@google-cloud/speech');
const vision = require('@google-cloud/vision');
const { GoogleGenAI } = require('@google/genai');
require('dotenv').config();

async function checkServices() {
    console.log('🔍 Checking service connections...\n');

    const results = {
        googleCloud: false,
        gemini: false,
        ffmpeg: false
    };

    // Check if we're in test mode
    const isTestMode = process.env.TEST_MODE === 'true';
    if (isTestMode) {
        console.log('🧪 TEST MODE ENABLED - Using mock services for testing\n');
        console.log('💡 TIP: Set TEST_MODE=false in .env to use real AI services (Phase 3 Complete!)');
    } else {
        console.log('🚀 PRODUCTION MODE - Using real Google Cloud Vision API and AI services\n');
    }

    // Check Google Cloud Speech API
    if (!isTestMode) {
        try {
            const speechClient = new speech.SpeechClient();
            // Try to get project info to verify credentials
            await speechClient.getProjectId();
            console.log('✅ Google Cloud Speech API: Connected');
            results.googleCloud = true;
        } catch (error) {
            console.log('❌ Google Cloud Speech API: Failed');
            console.log(`   Error: ${error.message}`);
        }
    } else {
        console.log('✅ Google Cloud Speech API: Mock mode enabled');
        results.googleCloud = true;
    }

    // Check Google Cloud Vision API
    if (!isTestMode) {
        try {
            const visionClient = new vision.ImageAnnotatorClient();
            console.log('✅ Google Cloud Vision API: Connected');
        } catch (error) {
            console.log('❌ Google Cloud Vision API: Failed');
            console.log(`   Error: ${error.message}`);
            results.googleCloud = false; // Override if vision fails
        }
    } else {
        console.log('✅ Google Cloud Vision API: Mock mode enabled');
    }

    // Check Gemini API
    if (!isTestMode) {
        try {
            if (!process.env.GEMINI_API_KEY) {
                throw new Error('GEMINI_API_KEY not set');
            }
            // Test with REST API first
            const testResponse = await axios.post(
                `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${process.env.GEMINI_API_KEY}`,
                {
                    contents: [{
                        parts: [{
                            text: 'Hello, this is a test message. Please respond with "Hello back!"'
                        }]
                    }]
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (testResponse.data && testResponse.data.candidates && testResponse.data.candidates[0]) {
                console.log('✅ Gemini API: Connected (via REST)');
                results.gemini = true;
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.log('❌ Gemini API: Failed');
            console.log(`   Error: ${error.message}`);
        }
    } else {
        console.log('✅ Gemini API: Mock mode enabled');
        results.gemini = true;
    }

    // Check FFmpeg
    if (!isTestMode) {
        try {
            const fs = require('fs');
            const path = require('path');
            const ffmpegPath = path.join(__dirname, '..', 'ffmpeg', 'ffmpeg-8.0-essentials_build', 'bin', 'ffmpeg.exe');
            const ffprobePath = path.join(__dirname, '..', 'ffmpeg', 'ffmpeg-8.0-essentials_build', 'bin', 'ffprobe.exe');

            if (fs.existsSync(ffmpegPath) && fs.existsSync(ffprobePath)) {
                console.log('✅ FFmpeg: Local binaries found');
                results.ffmpeg = true;
            } else {
                console.log('❌ FFmpeg: Local binaries not found');
                console.log('   Expected location:', ffmpegPath);
                console.log('   Please ensure FFmpeg binaries are in the ffmpeg/ directory');
            }
        } catch (error) {
            console.log('❌ FFmpeg: Check failed');
            console.log(`   Error: ${error.message}`);
        }
    } else {
        console.log('✅ FFmpeg: Mock mode enabled (skipping actual processing)');
        results.ffmpeg = true;
    }

    console.log('\n📊 Service Status Summary:');
    console.log(`Google Cloud APIs: ${results.googleCloud ? '✅' : '❌'}`);
    console.log(`Gemini AI: ${results.gemini ? '✅' : '❌'}`);
    console.log(`FFmpeg: ${results.ffmpeg ? '✅' : '❌'}`);

    const allServicesWorking = Object.values(results).every(status => status);

    if (allServicesWorking) {
        console.log('\n🎉 All services are working! You can start using the application.');
        if (isTestMode) {
            console.log('💡 TIP: Set TEST_MODE=false in .env to use real AI services (Phase 3 Complete!)');
        } else {
            console.log('🚀 Phase 3 Complete: Using real Google Cloud Vision API and AI services!');
        }
    } else {
        console.log('\n⚠️  Some services need attention. Please fix the issues above before running the application.');
        if (!isTestMode) {
            console.log('💡 TIP: Set TEST_MODE=true in .env to use mock services for testing');
        }
    }

    return allServicesWorking;
}

async function runFullTest() {
    console.log('\n🧪 Running Full Pipeline Test...\n');

    const fs = require('fs');
    const path = require('path');
    const videoController = require('../src/controllers/videoController');

    // Create a dummy video file for testing
    const testVideoPath = path.join(process.env.UPLOAD_DIR || 'uploads', 'test-video.mp4');
    if (!fs.existsSync(path.dirname(testVideoPath))) {
        fs.mkdirSync(path.dirname(testVideoPath), { recursive: true });
    }

    // Create a dummy video file (just some binary data)
    const dummyVideoData = Buffer.alloc(1024 * 1024); // 1MB dummy file
    fs.writeFileSync(testVideoPath, dummyVideoData);

    console.log(`📁 Created test video file: ${testVideoPath}`);

    // Create a mock request object
    const mockReq = {
        file: {
            path: testVideoPath,
            originalname: 'test-video.mp4',
            mimetype: 'video/mp4',
            size: dummyVideoData.length
        },
        startTime: Date.now()
    };

    // Create a mock response object
    const mockRes = {
        status: (code) => ({
            json: (data) => {
                console.log(`📤 Response Status: ${code}`);
                console.log('📄 Response Data:', JSON.stringify(data, null, 2));
                return mockRes;
            }
        }),
        json: (data) => {
            console.log('📄 Response Data:', JSON.stringify(data, null, 2));
            return mockRes;
        }
    };

    try {
        console.log('🚀 Starting video processing pipeline...');
        await videoController.processVideo(mockReq, mockRes);
        console.log('\n✅ Full pipeline test completed successfully!');
    } catch (error) {
        console.error('\n❌ Pipeline test failed:', error.message);
    } finally {
        // Cleanup test files
        try {
            if (fs.existsSync(testVideoPath)) {
                fs.unlinkSync(testVideoPath);
                console.log('🧹 Cleaned up test video file');
            }
        } catch (cleanupError) {
            console.warn('⚠️  Cleanup warning:', cleanupError.message);
        }
    }
}

// Check if we should run the full test
const shouldRunFullTest = process.argv.includes('--full-test');

if (shouldRunFullTest) {
    checkServices().then((servicesOk) => {
        if (servicesOk) {
            return runFullTest();
        } else {
            console.log('\n❌ Cannot run full test - services not ready');
            process.exit(1);
        }
    });
} else {
    checkServices().catch(console.error);
}