const speech = require('@google-cloud/speech');
const { Storage } = require('@google-cloud/storage');
const multiTranscriptionService = require('./multiTranscriptionService');
const fs = require('fs');
const path = require('path');

class SpeechToTextService {
    constructor() {
        this.client = new speech.SpeechClient();
        this.storage = new Storage();
        this.bucketName = 'video-cv-audio-transcription';
        this.multiTranscriber = multiTranscriptionService;
    }

    async transcribeAudio(audioPath) {
        try {
            console.log('🎤 Starting multi-service transcription...');

            // Use multi-service transcription with fallback
            const result = await this.multiTranscriber.transcribeAudio(audioPath, {
                languageCode: 'en-US',
                enableWordTimeOffsets: true
            });

            if (!result || !result.transcription) {
                throw new Error('All transcription services failed to produce valid results');
            }

            console.log(`✅ Transcription completed using ${result.service} (${result.transcription.length} characters)`);

            // Extract word timings if available
            const wordTimings = this.extractWordTimings(result);

            return {
                transcription: result.transcription,
                wordTimings: wordTimings,
                service: result.service,
                confidence: result.confidence || 0
            };

        } catch (error) {
            console.error('❌ All transcription services failed:', error);

            // Check if this is a transcription quality issue
            if (error.message?.includes('transcription') || error.message?.includes('quality')) {
                console.warn('⚠️  Low transcription quality detected. This may indicate:');
                console.warn('   - Poor audio quality or background noise');
                console.warn('   - Unsupported language or accent');
                console.warn('   - Very soft speech or microphone issues');
                console.warn('   - Consider using videos with clearer speech');
            }

            throw new Error(`Speech transcription failed: ${error.message}`);
        }
    }

    async transcribeShortAudio(audioPath) {
        // Read the audio file
        const audioBytes = fs.readFileSync(audioPath);
        const audio = {
            content: audioBytes.toString('base64'),
        };

        const request = {
            audio: audio,
            config: {
                encoding: 'LINEAR16',
                sampleRateHertz: 16000,
                languageCode: 'he-IL',
                enableWordTimeOffsets: true,
            },
        };

        // Perform the transcription
        const [response] = await this.client.recognize(request);

        // Extract transcription and word timings
        const transcription = response.results
            .map(result => result.alternatives[0].transcript)
            .join('\n');

        const wordTimings = response.results
            .flatMap(result => result.alternatives[0].words || []);

        return { transcription, wordTimings };
    }

    async uploadAudioToGCS(audioPath) {
        const fileName = `audio-${Date.now()}-${path.basename(audioPath)}`;
        const bucket = this.storage.bucket(this.bucketName);
        const file = bucket.file(fileName);

        console.log(`Uploading audio file to GCS: gs://${this.bucketName}/${fileName}`);

        await bucket.upload(audioPath, {
            destination: fileName,
            metadata: {
                contentType: 'audio/wav',
            },
        });

        console.log('Audio file uploaded successfully to GCS');
        return `gs://${this.bucketName}/${fileName}`;
    }

    async transcribeLongAudio(audioPath) {
        console.log('Using long-running recognition with GCS for large audio file...');

        try {
            // Upload audio file to GCS first
            const gcsUri = await this.uploadAudioToGCS(audioPath);

            const request = {
                audio: {
                    uri: gcsUri,
                },
                config: {
                    encoding: 'LINEAR16',
                    sampleRateHertz: 16000,
                    languageCode: 'he-IL',
                    enableWordTimeOffsets: true,
                },
            };

            // Start the long-running operation
            const [operation] = await this.client.longRunningRecognize(request);

            console.log('Long-running recognition operation started, waiting for completion...');

            // Wait for the operation to complete
            const [response] = await operation.promise();

            console.log('Long-running recognition completed successfully');

            // Extract transcription and word timings
            const transcription = response.results
                .map(result => result.alternatives[0].transcript)
                .join('\n');

            const wordTimings = response.results
                .flatMap(result => result.alternatives[0].words || []);

            // Clean up the GCS file
            try {
                const fileName = gcsUri.split('/').pop();
                await this.storage.bucket(this.bucketName).file(fileName).delete();
                console.log('Cleaned up audio file from GCS');
            } catch (cleanupError) {
                console.warn('Failed to clean up GCS file:', cleanupError.message);
            }

            return { transcription, wordTimings };

        } catch (error) {
            console.error('Long-running recognition failed:', error);
            throw error;
        }
    }

    // Analyze speech patterns for presentation skills assessment
    analyzeSpeechPatterns(wordTimings) {
        if (wordTimings.length === 0) return {};

        // Calculate speech rate (words per minute)
        const totalDuration = parseFloat(wordTimings[wordTimings.length - 1].endTime.seconds);
        const wordsPerMinute = (wordTimings.length / totalDuration) * 60;

        // Detect pauses (gaps between words)
        const pauses = [];
        for (let i = 1; i < wordTimings.length; i++) {
            const previousEnd = parseFloat(wordTimings[i-1].endTime.seconds);
            const currentStart = parseFloat(wordTimings[i].startTime.seconds);
            const pause = currentStart - previousEnd;
            if (pause > 0.5) { // Consider pauses longer than 0.5 seconds
                pauses.push(pause);
            }
        }

        return {
            wordsPerMinute: Math.round(wordsPerMinute),
            averagePause: pauses.length > 0 ? pauses.reduce((a,b)=>a+b,0)/pauses.length : 0,
            longestPause: pauses.length > 0 ? Math.max(...pauses) : 0,
            speechFluency: this.calculateFluencyScore(wordsPerMinute, pauses)
        };
    }

    // Extract word timings from multi-service result
    extractWordTimings(result) {
        // Handle different service formats
        if (result.wordTimings && Array.isArray(result.wordTimings)) {
            return result.wordTimings;
        }

        // For services that don't provide word timings, create basic structure
        if (result.transcription) {
            const words = result.transcription.split(' ');
            const estimatedDuration = words.length * 0.3; // Rough estimate: 0.3 seconds per word
            return words.map((word, index) => ({
                word: word,
                startTime: { seconds: (index * 0.3).toString() },
                endTime: { seconds: ((index + 1) * 0.3).toString() }
            }));
        }

        return [];
    }

    calculateFluencyScore(wpm, pauses) {
        // Optimal speaking rate is around 150-160 WPM for presentations
        let score = 100;

        if (wpm < 120) score -= 20; // Too slow
        else if (wpm > 200) score -= 30; // Too fast

        // Penalize excessive pauses
        if (pauses.length > 5) score -= (pauses.length - 5) * 5;

        return Math.max(0, Math.min(100, score));
    }
}

module.exports = new SpeechToTextService();