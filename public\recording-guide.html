<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video CV Generator - Recording Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 16px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px 16px 0 0;
            margin: -20px -20px 30px -20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .section h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .tip-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.3s ease;
        }

        .tip-card:hover {
            transform: translateY(-5px);
        }

        .tip-card h3 {
            color: #667eea;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tip-card ul {
            list-style: none;
            padding: 0;
        }

        .tip-card li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .tip-card li::before {
            content: "✅";
            position: absolute;
            left: 0;
            color: #48bb78;
        }

        .warning {
            background: #fed7d7;
            border: 1px solid #fc8181;
            border-left: 4px solid #e53e3e;
            color: #c53030;
        }

        .warning h2 {
            color: #e53e3e;
        }

        .warning li::before {
            content: "⚠️";
            color: #e53e3e;
        }

        .equipment {
            background: #c6f6d5;
            border: 1px solid #68d391;
            border-left: 4px solid #38a169;
        }

        .equipment h2 {
            color: #38a169;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px 10px 10px 0;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .demo-video {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 12px;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 30px 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .tips-grid {
                grid-template-columns: 1fr;
            }

            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 Perfect Video Recording Guide</h1>
            <p>Get the best results from your phone-recorded CV videos</p>
        </div>

        <div class="demo-video">
            <h2>📹 Watch Demo Video</h2>
            <p>See exactly how to record a professional CV video</p>
            <a href="#" class="btn">▶️ Watch 2-Minute Demo</a>
        </div>

        <div class="section">
            <h2>🎯 Recording Essentials</h2>
            <div class="tips-grid">
                <div class="tip-card">
                    <h3>📱 Device Setup</h3>
                    <ul>
                        <li>Use your phone's rear camera (better quality)</li>
                        <li>Hold phone horizontally (landscape mode)</li>
                        <li>Keep phone steady - use a tripod if possible</li>
                        <li>Ensure good lighting on your face</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <h3>🎤 Audio Quality</h3>
                    <ul>
                        <li>Speak 6-12 inches from the microphone</li>
                        <li>Use phone's built-in mic (usually best)</li>
                        <li>Record in a quiet room</li>
                        <li>Test audio levels before recording</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <h3>🎭 Presentation</h3>
                    <ul>
                        <li>Dress professionally</li>
                        <li>Look directly at the camera</li>
                        <li>Use natural gestures</li>
                        <li>Smile and show enthusiasm</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <h3>📝 Content Structure</h3>
                    <ul>
                        <li>Introduce yourself clearly</li>
                        <li>Describe your experience and skills</li>
                        <li>Explain why you're a great fit</li>
                        <li>Keep it under 3 minutes</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section warning">
            <h2>⚠️ Common Mistakes to Avoid</h2>
            <div class="tips-grid">
                <div class="tip-card warning">
                    <h3>🚫 Audio Problems</h3>
                    <ul>
                        <li>Background music or noise</li>
                        <li>Speaking too far from phone</li>
                        <li>Wind or echo in recording</li>
                        <li>Whispering or mumbling</li>
                    </ul>
                </div>

                <div class="tip-card warning">
                    <h3>🚫 Video Issues</h3>
                    <ul>
                        <li>Poor lighting or shadows</li>
                        <li>Shaky camera movement</li>
                        <li>Busy or distracting background</li>
                        <li>Recording in front camera</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section equipment">
            <h2>🎙️ Optional Equipment for Better Results</h2>
            <div class="tips-grid">
                <div class="tip-card equipment">
                    <h3>📱 Phone Accessories</h3>
                    <ul>
                        <li>Phone tripod or stand</li>
                        <li>Ring light for better lighting</li>
                        <li>External microphone (lavaliere)</li>
                        <li>Phone gimbal for stability</li>
                    </ul>
                </div>

                <div class="tip-card equipment">
                    <h3>🏠 Recording Environment</h3>
                    <ul>
                        <li>Quiet room with carpet/curtains</li>
                        <li>Professional backdrop</li>
                        <li>Soft lighting setup</li>
                        <li>Sound-absorbing materials</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Quick Start Checklist</h2>
            <div class="tip-card">
                <h3>✅ Pre-Recording</h3>
                <ul>
                    <li>Charge your phone fully</li>
                    <li>Find a quiet, well-lit space</li>
                    <li>Test audio and video quality</li>
                    <li>Prepare what you'll say</li>
                    <li>Dress professionally</li>
                </ul>

                <h3>✅ During Recording</h3>
                <ul>
                    <li>Speak clearly and confidently</li>
                    <li>Maintain eye contact with camera</li>
                    <li>Use natural hand gestures</li>
                    <li>Smile and show enthusiasm</li>
                    <li>Keep recording under 3 minutes</li>
                </ul>

                <h3>✅ After Recording</h3>
                <ul>
                    <li>Review the video for quality</li>
                    <li>Re-record if audio/video is poor</li>
                    <li>Upload to our platform</li>
                    <li>Get your professional CV!</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="/" class="btn">🎬 Start Recording My CV</a>
            <a href="/test-audio" class="btn" style="background: #48bb78;">🔊 Test My Audio</a>
        </div>
    </div>
</body>
</html>