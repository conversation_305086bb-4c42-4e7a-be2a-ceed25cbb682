const speech = require('@google-cloud/speech');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

async function testAudioTranscription() {
    console.log('🔍 Testing audio transcription quality...\n');

    try {
        // Path to the audio file from the last test
        const audioPath = path.join(__dirname, 'video-test_audio.wav');

        if (!fs.existsSync(audioPath)) {
            console.log('❌ Audio file not found:', audioPath);
            return;
        }

        // Check audio file stats
        const stats = fs.statSync(audioPath);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`📊 Audio file size: ${fileSizeMB} MB`);

        // Calculate expected duration (16kHz, 16-bit, mono WAV)
        const expectedDurationSeconds = stats.size / (16000 * 2);
        console.log(`⏱️  Expected duration: ${expectedDurationSeconds.toFixed(1)} seconds`);

        // Test with Speech API directly
        console.log('🎤 Testing Speech API directly...');

        const client = new speech.SpeechClient();
        const audioBytes = fs.readFileSync(audioPath);
        const audio = {
            content: audioBytes.toString('base64'),
        };

        const request = {
            audio: audio,
            config: {
                encoding: 'LINEAR16',
                sampleRateHertz: 16000,
                languageCode: 'he-IL',
                enableWordTimeOffsets: true,
                // Remove enhanced model for Hebrew
                // model: 'latest_long',
                // useEnhanced: true,
                enableAutomaticPunctuation: true,
            },
        };

        console.log('📤 Sending audio to Google Speech API...');
        const [response] = await client.recognize(request);

        console.log('✅ Speech API response received');

        // Analyze the response
        const transcription = response.results
            .map(result => result.alternatives[0].transcript)
            .join('\n');

        const wordTimings = response.results
            .flatMap(result => result.alternatives[0].words || []);

        console.log('\n📊 Transcription Analysis:');
        console.log(`📝 Transcription length: ${transcription.length} characters`);
        console.log(`📝 Word count: ${wordTimings.length}`);
        console.log(`📝 Number of result segments: ${response.results.length}`);

        // Calculate actual speaking time from word timings
        if (wordTimings.length > 0) {
            const firstWord = wordTimings[0];
            const lastWord = wordTimings[wordTimings.length - 1];

            const startTime = parseFloat(firstWord.startTime.seconds) + parseFloat(firstWord.startTime.nanos) / 1e9;
            const endTime = parseFloat(lastWord.endTime.seconds) + parseFloat(lastWord.endTime.nanos) / 1e9;

            const speakingDuration = endTime - startTime;
            console.log(`🎙️  Detected speaking time: ${speakingDuration.toFixed(1)} seconds`);
            console.log(`📊 Speaking ratio: ${(speakingDuration / expectedDurationSeconds * 100).toFixed(1)}% of total audio`);
        }

        // Show transcription content
        console.log('\n📝 Full Transcription:');
        console.log('=' .repeat(50));
        console.log(transcription || 'NO TRANSCRIPTION RECEIVED');
        console.log('=' .repeat(50));

        // Check for potential issues
        if (transcription.length < 100) {
            console.log('\n⚠️  WARNING: Transcription is very short!');
            console.log('Possible issues:');
            console.log('1. Audio quality too low');
            console.log('2. Language detection issues');
            console.log('3. Long periods of silence');
            console.log('4. Audio format issues');
            console.log('5. Speech API model limitations');
        }

        if (response.results.length === 0) {
            console.log('\n❌ No speech detected in audio!');
        }

        // Save detailed results
        const debugFile = path.join(__dirname, 'debug-direct-speech-api.json');
        fs.writeFileSync(debugFile, JSON.stringify({
            fileSize: stats.size,
            expectedDuration: expectedDurationSeconds,
            transcriptionLength: transcription.length,
            wordCount: wordTimings.length,
            resultCount: response.results.length,
            transcription: transcription,
            wordTimings: wordTimings.slice(0, 10) // First 10 words for debugging
        }, null, 2));
        console.log(`💾 Detailed results saved to: ${debugFile}`);

    } catch (error) {
        console.log('\n❌ Test failed:', error.message);

        if (error.details) {
            console.log('📄 Error details:', error.details);
        }

        if (error.code) {
            console.log('🔢 Error code:', error.code);
        }
    }
}

// Run the test
testAudioTranscription();