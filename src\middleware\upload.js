const multer = require('multer');
const path = require('path');

// Configure storage settings for uploaded videos
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, process.env.UPLOAD_DIR || 'uploads');
    },
    filename: (req, file, cb) => {
        // Generate unique filename to prevent conflicts
        const uniqueName = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueName + path.extname(file.originalname));
    }
});

// File validation to ensure we only accept video files
const fileFilter = (req, file, cb) => {
    console.log('📁 Upload middleware: checking file', file.originalname, file.mimetype);
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/webm'];

    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Only video files are allowed'), false);
    }
};

// Configure upload limits and validation
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit for video files
    }
});

module.exports = upload;