#!/usr/bin/env node

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Global error handler
process.on('uncaughtException', (err) => {
    console.error('❌ Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Import routes
const apiRoutes = require('./src/routes/api');

const app = express();
const port = process.env.PORT || 3000;

// Enable CORS
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));

// Parse JSON bodies
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// API routes
console.log('🔧 Loading API routes...');
app.use('/api', apiRoutes);
console.log('✅ API routes loaded');

// Simple test route - REMOVED to avoid conflicts

// Serve index.html for root path
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public/index.html'));
});

// Ensure directories exist
const dirs = ['uploads', 'output'];
dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    }
});

app.listen(port, '127.0.0.1', () => {
    console.log(`🚀 Video CV Generator server running on port ${port}`);
    console.log(`📁 Uploads directory: uploads`);
    console.log(`📤 Output directory: output`);
    console.log(`🌐 Frontend URL: http://localhost:3000`);
    console.log(`🔗 API endpoints available at http://localhost:${port}/api`);
});