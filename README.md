# Video CV Generator 🎥→📄

An AI-powered application that generates professional CVs from 30-second video introductions using computer vision, speech recognition, and natural language processing.

## 🌟 Features

- **Multimodal AI Analysis**: Combines audio transcription and visual analysis
- **Speech-to-Text**: Converts spoken content to structured text using Google Speech API
- **Computer Vision**: Analyzes presentation skills, eye contact, and professional appearance using Google Vision API
- **AI-Powered Extraction**: Uses Gemini AI to extract CV information from video content
- **Professional Templates**: Generates modern, ATS-friendly CV layouts
- **PDF Export**: Creates downloadable PDF versions of generated CVs
- **Presentation Assessment**: Provides feedback on communication skills and professionalism
- **Real-time Processing**: Full end-to-end video processing pipeline with real AI services

## 🏗️ Architecture

```
Video Upload → Audio/Visual Processing → AI Analysis → CV Generation → PDF Export
↓              ↓                       ↓              ↓
Validation    Speech-to-Text         Information      Template
Metadata      Computer Vision        Extraction       Generation
```

## 🚀 Quick Start

### Prerequisites

- Node.js 14.0.0 or higher
- FFmpeg installed on your system
- Google Cloud account with Speech and Vision APIs enabled
- Gemini API key from Google AI Studio
- Chrome browser (for PDF generation)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd video-cv-generator
npm install
```

2. **Run setup script:**
```bash
npm run setup
```

3. **Configure Google Cloud credentials:**
   - Create a Google Cloud project
   - Enable Speech-to-Text and Vision APIs
   - Create a service account and download the JSON key file
   - Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

4. **Update environment variables:**
Update the `.env` file with your credentials:
```
# Google Cloud Service Account
GOOGLE_APPLICATION_CREDENTIALS=./path/to/your/service-account.json

# Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development
```
GOOGLE_APPLICATION_CREDENTIALS=./path/to/service-account.json

# Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development
```

4. **Verify service connections:**
```bash
npm run check-services
```

5. **Start the development server:**
```bash
npm run dev
```

6. **Open your browser:**
Navigate to `http://localhost:3000`

## 📋 API Endpoints

### POST `/api/process-video`

Processes uploaded video and generates CV.

**Request:**
- `video`: Video file (multipart/form-data)
- Supported formats: MP4, AVI, MOV, WEBM
- Max size: 100MB
- Recommended duration: ~30 seconds

**Response:**
```json
{
  "success": true,
  "cvData": { /* Extracted CV information */ },
  "cv_html": "<!-- Generated CV HTML -->",
  "pdf_url": "/download/cv_123456789.pdf",
  "analysis_summary": { /* Presentation analysis */ },
  "processing_stats": { /* Performance metrics */ }
}
```

### GET `/api/download/:filename`

Downloads generated PDF CV.

### GET `/api/health`

Service health check.

### GET `/api/supported-formats`

Returns supported video formats and requirements.

## 🎯 How It Works

### 1. Video Processing Pipeline
```jsx
Video Upload → Validation → Audio Extraction → Frame Extraction
```

### 2. Parallel AI Analysis
```jsx
Audio Stream → Speech-to-Text → Speech Pattern Analysis
Video Frames → Computer Vision → Presentation Assessment
```

### 3. Information Extraction
```jsx
Transcript + Visual Analysis → Gemini AI → Structured CV Data
```

### 4. CV Generation
```jsx
CV Data → HTML Template → PDF Generation → Download
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
| --- | --- | --- |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to Google Cloud service account JSON | Yes |
| `GEMINI_API_KEY` | Gemini AI API key | Yes |
| `PORT` | Server port (default: 3000) | No |
| `UPLOAD_DIR` | Upload directory (default: uploads) | No |
| `OUTPUT_DIR` | Output directory (default: output) | No |
| `NODE_ENV` | Environment (development/production) | No |

### Google Cloud Setup

1. Create a Google Cloud project
2. Enable Speech-to-Text and Vision APIs
3. Create a service account
4. Download the service account JSON key
5. Set `GOOGLE_APPLICATION_CREDENTIALS` to the JSON file path

### Gemini API Setup

1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Create an API key
3. Set `GEMINI_API_KEY` in your environment

## 📊 Analysis Features

### Speech Analysis
- **Transcription accuracy**: Confidence scoring
- **Speech patterns**: Words per minute, pause analysis
- **Fluency assessment**: Professional speaking evaluation

### Visual Analysis
- **Face detection**: Professional appearance assessment
- **Eye contact**: Camera engagement measurement
- **Background analysis**: Professional setting evaluation
- **Text detection**: Visible credentials/certificates

### Content Extraction
- **Personal information**: Name, contact details
- **Professional summary**: Experience, role, objectives
- **Skills**: Technical and soft skills identification
- **Work experience**: Roles, companies, achievements
- **Education**: Degrees, institutions, certifications

## 🎨 CV Templates

The system generates modern, professional CV templates with:
- Clean, ATS-friendly layout
- Responsive design for all devices
- Professional color schemes
- Structured sections for easy reading
- Presentation assessment scores
- Print-optimized formatting

## 🛠️ Development

### Project Structure
```
video-cv-generator/
├── src/
│   ├── controllers/     # Request handlers
│   ├── services/        # AI service integrations
│   ├── middleware/      # Express middleware
│   ├── routes/         # API route
│   ├── templates/      # CV templates
│   └── utils/          # Helper functions
├── uploads/            # Temporary file storage
├── output/             # Generated CVs
├── public/            # Static frontend files
└── scripts/           # Setup and utility scripts
```

### Available Scripts
- `npm start`: Start production server
- `npm run dev`: Start development server with auto-restart
- `npm run setup`: Initial project setup
- `npm run check-services`: Verify API connections
- `npm run cleanup`: Clean temporary files

## 🎉 Phase 3 Complete: Computer Vision Integration

**Phase 3: Computer Vision Integration** has been successfully implemented! The system now uses real Google Cloud Vision API for advanced visual analysis including:

- **Facial Analysis**: Emotion detection, eye contact assessment, confidence evaluation
- **Object Recognition**: Professional environment analysis, background assessment
- **Text Detection**: Credential and qualification recognition from visible text
- **Presentation Quality**: Comprehensive scoring of communication skills and professionalism
- **Real-time Processing**: Full end-to-end pipeline with actual AI services (no more mock data)

### Key Phase 3 Features:
- ✅ Real Google Vision API integration for facial analysis
- ✅ Advanced emotion and confidence detection
- ✅ Professional environment assessment
- ✅ Credential text recognition
- ✅ Real FFmpeg video processing
- ✅ Live Google Speech-to-Text API
- ✅ Production-ready Gemini AI enhancement
- ✅ Full PDF generation with Chrome browser

The Video-to-CV Generation System is now production-ready with complete computer vision capabilities!

## � Testing

The project includes comprehensive test scripts to validate functionality:

### Test Scripts

1. **Mock Test** (Fast - ~30 seconds):
   ```bash
   node test-mock-transcription.js
   ```
   - Tests API structure and error handling
   - Uses invalid video file to verify graceful failure
   - Perfect for development and CI/CD

2. **Full Test** (Slow - 2-10 minutes):
   ```bash
   node test-transcription-access.js
   ```
   - Complete end-to-end video processing
   - Validates transcription access and AI analysis
   - Uses real video file for comprehensive testing

3. **Test Runner** (Comprehensive):
   ```bash
   node test-runner.js
   ```
   - Runs all available tests with summary
   - Checks server health before testing
   - Provides detailed results and debugging tips

### Test Features

- ✅ **Transcription Access**: Verifies transcription data is available in API responses
- ✅ **Analysis Summary**: Validates visual and speech metrics extraction
- ✅ **Error Handling**: Tests graceful failure for invalid inputs
- ✅ **Server Health**: Checks API endpoints before running tests
- ✅ **Debug Output**: Saves transcription and response data for debugging

### Test Files Generated

- `debug-transcription.txt`: Full transcription text for debugging
- `debug-response.json`: Complete API response for analysis
- `short-test.mp4`: 10-second test video clip (created automatically)

### Known Limitations

⚠️ **Speech Recognition Quality**: The system relies on Google Speech API for transcription. Some videos may have limited transcription due to:
- Poor audio quality or background noise
- Unsupported languages or strong accents
- Very soft speech or microphone issues
- Long periods of silence

**Quality Indicators**: The API response includes transcription quality warnings when detected. For optimal results:
- Use clear, well-lit videos with good audio
- Speak clearly and at normal pace (120-180 words/minute)
- Minimize background noise
- Test with shorter videos first

### Running Tests

```bash
# Start the server first
node server.js

# In another terminal, run tests
node test-runner.js
```

**Note**: Full video processing tests may take several minutes. Use mock tests for quick validation during development.

## �🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## ⚠️ Disclaimer

This tool uses AI services and generates content based on video analysis. Always review and verify generated CVs before use. The accuracy of extracted information depends on video quality and content clarity.