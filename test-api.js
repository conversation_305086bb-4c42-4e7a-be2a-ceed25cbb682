const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testVideoProcessing() {
    try {
        const form = new FormData();
        form.append('video', fs.createReadStream('./video-test.mp4'));

        console.log('🚀 Testing video processing API...');

        const response = await axios.post('http://localhost:3001/api/process-video', form, {
            headers: form.getHeaders(),
            timeout: 300000 // 5 minutes timeout
        });

        console.log('✅ API Response:', response.data);

    } catch (error) {
        console.error('❌ API Error:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            stack: error.stack
        });
    }
}

testVideoProcessing();