const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function runTest(testFile, description) {
    return new Promise((resolve) => {
        console.log(`\n🚀 Running: ${description}`);
        console.log('='.repeat(50));

        const testProcess = spawn('node', [testFile], {
            cwd: __dirname,
            stdio: 'inherit'
        });

        testProcess.on('close', (code) => {
            console.log(`\n✅ ${description} completed with exit code: ${code}`);
            resolve(code === 0);
        });

        testProcess.on('error', (error) => {
            console.log(`❌ ${description} failed:`, error.message);
            resolve(false);
        });
    });
}

async function runAllTests() {
    console.log('🧪 Video-to-CV System Test Suite');
    console.log('================================\n');

    const tests = [
        {
            file: 'test-mock-transcription.js',
            description: 'Mock Transcription Access Test (Fast)',
            required: true
        },
        {
            file: 'test-transcription-access.js',
            description: 'Full Transcription Access Test (Slow)',
            required: false
        }
    ];

    const results = [];

    for (const test of tests) {
        if (fs.existsSync(path.join(__dirname, test.file))) {
            const success = await runTest(test.file, test.description);
            results.push({ ...test, success });
        } else {
            console.log(`⚠️  Skipping ${test.description} - file not found: ${test.file}`);
            results.push({ ...test, success: false, skipped: true });
        }
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));

    let passed = 0;
    let failed = 0;
    let skipped = 0;

    results.forEach(result => {
        if (result.skipped) {
            console.log(`⏭️  ${result.description}: SKIPPED`);
            skipped++;
        } else if (result.success) {
            console.log(`✅ ${result.description}: PASSED`);
            passed++;
        } else {
            console.log(`❌ ${result.description}: FAILED`);
            failed++;
        }
    });

    console.log('\n📈 Results:');
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   ⏭️  Skipped: ${skipped}`);

    if (failed === 0 && skipped === 0) {
        console.log('\n🎉 All tests passed!');
    } else if (failed === 0) {
        console.log('\n⚠️  Some tests were skipped, but no failures.');
    } else {
        console.log('\n💥 Some tests failed. Check the output above for details.');
    }

    console.log('\n💡 Tips:');
    console.log('   - Mock test: Quick validation of API structure');
    console.log('   - Full test: Complete end-to-end validation (2-10 minutes)');
    console.log('   - Run individual tests: node test-mock-transcription.js');
    console.log('   - Debug files: Check debug-transcription.txt and debug-response.json');

    return { passed, failed, skipped };
}

// Check if server is running
async function checkServer() {
    const http = require('http');

    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: 3001,
            path: '/api/health',
            method: 'GET',
            timeout: 5000
        }, (res) => {
            resolve(true);
        });

        req.on('error', () => resolve(false));
        req.on('timeout', () => {
            req.destroy();
            resolve(false);
        });

        req.end();
    });
}

async function main() {
    console.log('🔍 Checking server status...');
    const serverRunning = await checkServer();

    if (!serverRunning) {
        console.log('❌ Server is not running on http://localhost:3001');
        console.log('💡 Start the server first: node server.js');
        process.exit(1);
    }

    console.log('✅ Server is running');

    await runAllTests();
}

main();