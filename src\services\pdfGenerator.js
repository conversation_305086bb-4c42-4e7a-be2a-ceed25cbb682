const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

class PDFGenerator {

    constructor() {
        // Configure Puppeteer to use the installed Chrome
        this.chromePath = 'C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-140.0.7339.207\\chrome-win64\\chrome.exe';
    }

    async generatePDF(htmlContent, outputFileName) {
        // Check if we're in test mode
        if (process.env.TEST_MODE === 'true') {
            console.log('🧪 TEST MODE: Mocking PDF generation');
            const outputPath = path.join(process.env.OUTPUT_DIR || 'output', `${outputFileName}.pdf`);
            
            // Ensure output directory exists
            const outputDir = path.dirname(outputPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            // Create a dummy PDF file
            fs.writeFileSync(outputPath, 'mock pdf content');
            return { success: true, path: outputPath, url: `/download/${outputFileName}.pdf` };
        }

        const outputPath = path.join(process.env.OUTPUT_DIR || 'output', `${outputFileName}.pdf`);

        try {
            // Launch browser with correct Chrome path
            const browser = await puppeteer.launch({
                executablePath: this.chromePath,
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
            });

            const page = await browser.newPage();
            
            // Set content and generate PDF
            await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
            
            const pdfBuffer = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '0.5in',
                    right: '0.5in',
                    bottom: '0.5in',
                    left: '0.5in'
                },
                preferCSSPageSize: true,
                displayHeaderFooter: false
            });

            await browser.close();

            // Ensure output directory exists
            const outputDir = path.dirname(outputPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            // Write PDF to file
            fs.writeFileSync(outputPath, pdfBuffer);

            return { 
                success: true, 
                path: outputPath, 
                url: `/download/${outputFileName}.pdf` 
            };

        } catch (error) {
            console.error('PDF generation failed:', error);
            throw new Error(`PDF generation failed: ${error.message}`);
        }
    }

    async generatePreviewImages(htmlContent, outputFileName) {
        // This would generate preview images of the CV
        // Implementation would depend on specific requirements
        // For now, return placeholder
        console.log(`Preview image generation for ${outputFileName} - not implemented`);
        return { success: false, message: 'Preview generation not implemented' };
    }
}

module.exports = new PDFGenerator();