const TranscriptionService = require('./src/services/multiTranscriptionService');

async function testTranscription() {
    console.log('🧪 Testing transcription service...');

    try {
        // Create a simple test audio file
        const fs = require('fs');
        const path = require('path');

        // Use the short audio file if it exists
        const testAudioPath = path.join(__dirname, 'short-audio.wav');
        if (fs.existsSync(testAudioPath)) {
            console.log('Using existing test audio file...');
            const result = await TranscriptionService.transcribeAudio(testAudioPath);
            console.log('✅ Transcription result:', result);
        } else {
            console.log('❌ Test audio file not found');
        }
    } catch (error) {
        console.error('❌ Transcription test failed:', error.message);
    }
}

testTranscription();