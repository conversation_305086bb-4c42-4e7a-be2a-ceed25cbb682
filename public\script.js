// Global variables
let selectedFile = null;
let processingInterval = null;
let currentProgress = 0;
let sessionId = null;

// Processing steps with messages and estimated progress
const processingSteps = [
    { message: "Validating video file...", progress: 5 },
    { message: "Extracting audio from video...", progress: 10 },
    { message: "Transcribing speech to text...", progress: 25 },
    { message: "Analyzing speech patterns...", progress: 35 },
    { message: "Extracting video frames...", progress: 45 },
    { message: "Analyzing visual content...", progress: 60 },
    { message: "Extracting CV information...", progress: 70 },
    { message: "Validating CV data...", progress: 75 },
    { message: "Enhancing CV content...", progress: 80 },
    { message: "Generating professional CV...", progress: 85 },
    { message: "Creating PDF document...", progress: 95 },
    { message: "Finalizing your CV...", progress: 100 }
];

document.addEventListener('DOMContentLoaded', function() {
    initializeDragAndDrop();
    setupFileInput();
});

function initializeDragAndDrop() {
    const uploadArea = document.getElementById('uploadArea');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadArea.classList.add('dragover');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    }
}

function setupFileInput() {
    const fileInput = document.getElementById('videoFile');
    const uploadArea = document.getElementById('uploadArea');

    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    fileInput.addEventListener('change', function(e) {
        if (this.files.length > 0) {
            handleFileSelect(this.files[0]);
        }
    });
}

function handleFileSelect(file) {
    // Validate file type
    if (!file.type.startsWith('video/')) {
        showError('Please select a valid video file.');
        return;
    }

    // Validate file size (500MB limit)
    const maxSize = 500 * 1024 * 1024; // 500MB
    if (file.size > maxSize) {
        showError('File size must be less than 500MB.');
        return;
    }

    selectedFile = file;
    updateFileInfo(file);
    hideError();

    // Enable process button
    document.getElementById('processBtn').disabled = false;
}

function updateFileInfo(file) {
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');

    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);

    fileInfo.classList.remove('hide');
    fileInfo.classList.add('show');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hide');
    errorDiv.classList.add('show');
}

function hideError() {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.classList.remove('show');
    errorDiv.classList.add('hide');
}

async function processVideo() {
    if (!selectedFile) {
        showError('Please select a video file first.');
        return;
    }

    hideError();
    showProcessingOverlay();

    // Generate unique session ID for this processing request
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Start real progress tracking
    startProgressTracking();

    try {
        const formData = new FormData();
        formData.append('video', selectedFile);
        formData.append('sessionId', sessionId);

        const response = await fetch('/api/process-video', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            displayResults(result);
        } else {
            throw new Error(result.details || result.error || 'Processing failed');
        }
    } catch (error) {
        console.error('Error processing video:', error);
        showError('Error processing video: ' + error.message);
    } finally {
        hideProcessingOverlay();
        stopProgressTracking();
    }
}

function showProcessingOverlay() {
    document.getElementById('processingOverlay').classList.add('show');
}

function hideProcessingOverlay() {
    document.getElementById('processingOverlay').classList.remove('show');
}

function startProgressTracking() {
    currentProgress = 0;
    let timeoutCounter = 0;
    const MAX_TIMEOUT = 300; // 5 minutes max

    processingInterval = setInterval(async () => {
        try {
            timeoutCounter++;
            
            // If we've been polling for too long without completion, show an error
            if (timeoutCounter > MAX_TIMEOUT) {
                console.error('Processing timeout - taking too long');
                stopProgressTracking();
                showError('Processing is taking longer than expected. Please try again with a shorter video.');
                return;
            }

            const response = await fetch(`/api/progress/${sessionId}`, {
                timeout: 5000 // 5 second timeout for each request
            });
            const progress = await response.json();

            updateProgress(progress.message, progress.progress);

            // Reset timeout counter on progress
            if (progress.progress > currentProgress) {
                timeoutCounter = 0;
                currentProgress = progress.progress;
            }

            // Stop polling when complete
            if (progress.progress >= 100) {
                stopProgressTracking();
            }
        } catch (error) {
            console.warn('Failed to fetch progress:', error);
            // Continue polling even if there's an error
        }
    }, 1000); // Poll every second
}

function stopProgressTracking() {
    if (processingInterval) {
        clearInterval(processingInterval);
        processingInterval = null;
    }
    sessionId = null;
    updateProgress('Complete!', 100);
}

function updateProgress(message, progress) {
    document.getElementById('processingMessage').textContent = message;
    document.getElementById('progressFill').style.width = progress + '%';
    document.getElementById('progressText').textContent = progress + '% Complete';
    currentProgress = progress;
}

function displayResults(data) {
    const resultsSection = document.getElementById('resultsSection');
    const cvPreview = document.getElementById('cvPreview');
    const analysisSummary = document.getElementById('analysisSummary');

    // Display CV HTML
    cvPreview.innerHTML = data.cv_html || '<p>CV preview not available</p>';

    // Display analysis summary (without transcription for cleaner UI)
    if (data.analysis_summary) {
        analysisSummary.innerHTML = formatAnalysisSummary(data.analysis_summary);
    }

    // Show results section
    resultsSection.classList.add('show');

    // Scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function formatAnalysisSummary(summary) {
    let html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">';

    if (summary.visualAnalysis) {
        html += `
            <div style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h4 style="color: #667eea; margin-bottom: 1rem; font-size: 1.2em;">👁️ Visual Analysis</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #e8f0ff 0%, #d4e4ff 100%); border-radius: 8px;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #667eea; margin-bottom: 0.5rem;">${summary.visualAnalysis.professionalScore}</div>
                        <div style="font-size: 0.9em; color: #666;">Professional Score</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #e8f0ff 0%, #d4e4ff 100%); border-radius: 8px;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #667eea; margin-bottom: 0.5rem;">${summary.visualAnalysis.eyeContact?.score || 0}%</div>
                        <div style="font-size: 0.9em; color: #666;">Eye Contact</div>
                        <div style="font-size: 0.8em; color: #888; margin-top: 0.25rem;">${summary.visualAnalysis.eyeContact?.assessment || 'N/A'}</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #e8f0ff 0%, #d4e4ff 100%); border-radius: 8px;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #667eea; margin-bottom: 0.5rem;">${summary.visualAnalysis.background?.score || 0}%</div>
                        <div style="font-size: 0.9em; color: #666;">Background</div>
                        <div style="font-size: 0.8em; color: #888; margin-top: 0.25rem;">${summary.visualAnalysis.background?.assessment || 'N/A'}</div>
                    </div>
                </div>
            </div>
        `;
    }

    if (summary.speechAnalysis) {
        html += `
            <div style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h4 style="color: #667eea; margin-bottom: 1rem; font-size: 1.2em;">🎤 Speech Analysis</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #fff5e8 0%, #ffe4d4 100%); border-radius: 8px;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #ff8c42; margin-bottom: 0.5rem;">${summary.speechAnalysis.wordsPerMinute}</div>
                        <div style="font-size: 0.9em; color: #666;">Words/Minute</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #fff5e8 0%, #ffe4d4 100%); border-radius: 8px;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #ff8c42; margin-bottom: 0.5rem;">${summary.speechAnalysis.averagePause?.toFixed(1)}s</div>
                        <div style="font-size: 0.9em; color: #666;">Avg Pause</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #fff5e8 0%, #ffe4d4 100%); border-radius: 8px;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #ff8c42; margin-bottom: 0.5rem;">${summary.speechAnalysis.longestPause?.toFixed(1)}s</div>
                        <div style="font-size: 0.9em; color: #666;">Longest Pause</div>
                    </div>
                </div>
            </div>
        `;
    }

    html += '</div>';
    return html;
}

function downloadPDF() {
    // Find the PDF URL from the results
    const pdfLinks = document.querySelectorAll('a[href*=".pdf"]');
    if (pdfLinks.length > 0) {
        pdfLinks[0].click();
        return;
    }

    // Fallback: try to construct the URL
    const timestamp = Date.now();
    const link = document.createElement('a');
    link.href = `/download/cv_${timestamp}.pdf`;
    link.download = `cv_${timestamp}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}