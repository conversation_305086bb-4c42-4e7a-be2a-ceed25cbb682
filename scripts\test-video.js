const videoProcessor = require('../src/services/videoProcessor');

async function testVideoProcessing() {
    console.log('🧪 Testing video processing...\n');

    try {
        const videoPath = './video-test.mp4';
        console.log(`📹 Processing video: ${videoPath}`);

        // Test metadata extraction
        console.log('📊 Extracting metadata...');
        const metadata = await videoProcessor.getVideoMetadata(videoPath);
        console.log('✅ Metadata extracted:', {
            duration: metadata.format.duration,
            size: metadata.format.size,
            videoStreams: metadata.streams.filter(s => s.codec_type === 'video').length,
            audioStreams: metadata.streams.filter(s => s.codec_type === 'audio').length
        });

        // Test validation
        console.log('🔍 Validating video...');
        const validation = videoProcessor.validateVideo(metadata);
        console.log('✅ Validation result:', validation);

        if (validation.isValid) {
            console.log('🎉 Video processing test passed!');
        } else {
            console.log('❌ Video validation failed:', validation.errors);
        }

    } catch (error) {
        console.error('❌ Video processing test failed:', error.message);
    }
}

testVideoProcessing();