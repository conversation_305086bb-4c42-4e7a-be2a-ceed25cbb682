const { Storage } = require('@google-cloud/storage');
const speech = require('@google-cloud/speech');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

async function testGCSBucket() {
    console.log('🔍 Testing GCS bucket access...\n');

    try {
        const storage = new Storage();
        const bucketName = 'video-cv-audio-transcription';
        const bucket = storage.bucket(bucketName);

        // Check if bucket exists
        const [exists] = await bucket.exists();
        console.log(`📦 Bucket '${bucketName}' exists: ${exists}`);

        if (!exists) {
            console.log('❌ Bucket does not exist! This will cause transcription to fail.');
            console.log('💡 You need to create the GCS bucket first.');
            return false;
        }

        // Try to list objects (to check permissions)
        const [files] = await bucket.getFiles({ maxResults: 1 });
        console.log(`✅ Bucket accessible, found ${files.length} files`);

        return true;

    } catch (error) {
        console.log('❌ GCS bucket test failed:', error.message);
        return false;
    }
}

async function testLongRunningTranscription() {
    console.log('\n🔍 Testing long-running transcription...\n');

    try {
        const audioPath = path.join(__dirname, 'short-audio.wav');

        if (!fs.existsSync(audioPath)) {
            console.log('❌ Audio file not found:', audioPath);
            return;
        }

        // Check file size
        const stats = fs.statSync(audioPath);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`📊 Audio file size: ${fileSizeMB} MB`);

        // Upload to GCS first
        console.log('📤 Uploading audio to GCS...');
        const storage = new Storage();
        const bucket = storage.bucket('video-cv-audio-transcription');
        const fileName = `test-audio-${Date.now()}.wav`;
        const file = bucket.file(fileName);

        await bucket.upload(audioPath, {
            destination: fileName,
            metadata: {
                contentType: 'audio/wav',
            },
        });

        console.log('✅ Audio uploaded to GCS');

        // Test long-running recognition
        console.log('🎤 Starting long-running recognition...');
        const client = new speech.SpeechClient();

        const request = {
            audio: {
                uri: `gs://video-cv-audio-transcription/${fileName}`,
            },
            config: {
                encoding: 'LINEAR16',
                sampleRateHertz: 16000,
                languageCode: 'he-IL', // Back to Hebrew
                enableWordTimeOffsets: true,
                enableAutomaticPunctuation: true,
            },
        };

        const [operation] = await client.longRunningRecognize(request);
        console.log('✅ Long-running operation started');

        // Wait for completion (with timeout)
        console.log('⏳ Waiting for transcription to complete...');
        const [response] = await operation.promise();

        console.log('✅ Transcription completed');

        // Analyze results
        const transcription = response.results
            .map(result => result.alternatives[0].transcript)
            .join('\n');

        const wordTimings = response.results
            .flatMap(result => result.alternatives[0].words || []);

        console.log('\n📊 Results:');
        console.log(`📝 Transcription length: ${transcription.length} characters`);
        console.log(`📝 Word count: ${wordTimings.length}`);
        console.log(`📝 Result segments: ${response.results.length}`);

        if (transcription.length < 100) {
            console.log('\n⚠️  WARNING: Transcription is very short for a long audio file!');
        }

        // Show sample transcription
        console.log('\n📝 Sample transcription:');
        console.log(transcription.substring(0, 200) + (transcription.length > 200 ? '...' : ''));

        // Clean up
        await file.delete();
        console.log('🧹 Cleaned up test file from GCS');

        return { transcription, wordCount: wordTimings.length };

    } catch (error) {
        console.log('❌ Long-running transcription test failed:', error.message);
        if (error.details) {
            console.log('📄 Error details:', error.details);
        }
        return null;
    }
}

async function runTests() {
    const bucketOk = await testGCSBucket();
    if (!bucketOk) {
        console.log('\n❌ Cannot proceed without GCS bucket access');
        return;
    }

    await testLongRunningTranscription();
}

runTests();