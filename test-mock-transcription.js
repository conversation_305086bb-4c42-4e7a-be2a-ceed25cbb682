const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testMockTranscriptionAccess() {
    console.log('🧪 Testing transcription access with mock data...\n');

    try {
        // Create a simple mock video file (just for testing the upload)
        const mockVideoPath = path.join(__dirname, 'mock-test.mp4');

        // Create a minimal MP4 file for testing (this won't be processed)
        const minimalMp4Data = Buffer.from([
            0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70,
            0x6D, 0x70, 0x34, 0x32, 0x00, 0x00, 0x00, 0x00,
            0x6D, 0x70, 0x34, 0x32, 0x6D, 0x70, 0x34, 0x31,
            0x69, 0x73, 0x6F, 0x6D, 0x00, 0x00, 0x00, 0x00
        ]);

        fs.writeFileSync(mockVideoPath, minimalMp4Data);
        console.log('📹 Created mock video file:', mockVideoPath);

        // Test the API with shorter timeout
        console.log('📤 Sending request to /api/process-video (mock test)...');
        console.log('⏳ This should fail quickly since mock video is invalid...');

        const FormData = require('form-data');
        const form = new FormData();
        form.append('video', fs.createReadStream(mockVideoPath));
        form.append('sessionId', 'mock-test-' + Date.now());

        // Shorter timeout for mock test
        const response = await axios.post('http://localhost:3001/api/process-video', form, {
            headers: {
                ...form.getHeaders(),
                'Content-Type': 'multipart/form-data'
            },
            timeout: 30000, // 30 seconds
        });

        console.log('✅ Mock test completed (unexpected success)');

    } catch (error) {
        console.log('\n✅ Mock test completed as expected (processing failed for invalid video)');
        console.log('📊 Error Analysis:');

        if (error.code === 'ECONNABORTED') {
            console.log('⏰ Request timed out - this is expected for invalid video processing');
        } else if (error.response) {
            console.log('📡 Response status:', error.response.status);
            console.log('📄 Error message:', error.response.data?.error || error.response.data);

            // Check if transcription access is mentioned in error
            if (error.response.data?.transcription) {
                console.log('✅ Transcription data available even in error response!');
            }
        } else {
            console.log('🔍 Error type:', error.message);
        }

        console.log('\n💡 Mock test purpose: Verify error handling and quick failure for invalid inputs');
        console.log('💡 Real video processing should work as shown in the full test');
    } finally {
        // Clean up mock file
        const mockVideoPath = path.join(__dirname, 'mock-test.mp4');
        if (fs.existsSync(mockVideoPath)) {
            fs.unlinkSync(mockVideoPath);
            console.log('🧹 Cleaned up mock video file');
        }
    }
}

// Test health endpoint first
async function testHealth() {
    try {
        console.log('🏥 Testing server health...');
        const response = await axios.get('http://localhost:3001/api/health', { timeout: 5000 });
        console.log('✅ Server is healthy');
        return true;
    } catch (error) {
        console.log('❌ Server health check failed:', error.message);
        console.log('💡 Make sure the server is running: node server.js');
        return false;
    }
}

// Run tests
async function runTests() {
    const serverHealthy = await testHealth();
    if (!serverHealthy) return;

    console.log('\n' + '='.repeat(50));
    await testMockTranscriptionAccess();
}

runTests();