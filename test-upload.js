const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testVideoUpload() {
    try {
        const form = new FormData();
        form.append('video', fs.createReadStream('./video-test.mp4'));
        form.append('name', 'Test User');
        form.append('email', '<EMAIL>');

        console.log('Uploading video for processing...');

        const response = await axios.post('http://localhost:3000/api/process-video', form, {
            headers: form.getHeaders(),
            maxContentLength: Infinity,
            maxBodyLength: Infinity
        });

        console.log('Upload successful!');
        console.log('Response:', JSON.stringify(response.data, null, 2));

    } catch (error) {
        console.error('Upload failed:', error.response?.data || error.message);
    }
}

testVideoUpload();