# Complete AI Agent Instructions: Video-to-CV Generation System

## Project Overview and Learning Foundation

Before we dive into the technical implementation, let's establish a clear understanding of what we're building and why each component matters. Think of this system as a pipeline where raw video data flows through multiple AI processing stages to emerge as a structured, professional CV.

The core challenge here is multimodal AI processing - we need to extract meaningful information from both audio and visual streams simultaneously, then synthesize this data into a coherent professional profile. This is similar to how a human recruiter might evaluate a candidate during a video interview, noting both what they say and how they present themselves.

## System Architecture Understanding

The system follows a microservices architecture where each component has a specific responsibility. The frontend captures and uploads video, the backend orchestrates the AI processing pipeline, and various free AI services handle specialized tasks like speech recognition, computer vision, and natural language processing. This separation of concerns makes the system more maintainable and allows us to swap out individual components as better free alternatives become available.

## Phase 1: Project Setup and Environment Configuration

### Task 1.1: Initialize Node.js Backend Foundation

Create a new Node.js project that will serve as our orchestration layer. The backend needs to handle file uploads, coordinate multiple AI service calls, and manage the data flow between services.

**Subtask 1.1.1: Project Structure Creation**

```bash
mkdir video-cv-generator
cd video-cv-generator
npm init -y

```

Create the following directory structure to organize our code logically:

```
video-cv-generator/
├── src/
│   ├── controllers/     # Handle HTTP requests and responses
│   ├── services/        # Business logic and AI service integrations
│   ├── utils/          # Helper functions and utilities
│   ├── middleware/     # Express middleware functions
│   └── routes/         # API route definitions
├── uploads/            # Temporary file storage
├── output/            # Generated CVs
└── public/           # Static files for frontend

```

**Subtask 1.1.2: Essential Dependencies Installation**
Install the core packages we'll need for our multimodal processing pipeline:

```bash
npm install express multer cors dotenv
npm install @google-cloud/speech @google-cloud/vision
npm install axios form-data
npm install fluent-ffmpeg
npm install jspdf html-pdf-node
npm install --save-dev nodemon

```

Let me explain why each dependency is crucial: Express handles our HTTP server and routing, Multer manages file uploads, Google Cloud libraries provide our free AI services, Axios handles HTTP requests to external APIs, Fluent-FFmpeg processes video files, and the PDF libraries generate our final CV output.

**Subtask 1.1.3: Environment Configuration**
Create a `.env` file to manage our API credentials and configuration:

```
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account.json
GEMINI_API_KEY=your_gemini_api_key
PORT=3000
UPLOAD_DIR=uploads
OUTPUT_DIR=output

```

### Task 1.2: Frontend Interface Development

**Subtask 1.2.1: HTML Upload Interface Creation**
Create a simple but functional upload interface in `public/index.html`:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Video CV Generator</title>
    <style>
        /* Add styling for professional appearance */
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .processing { display: none; }
        .results { margin-top: 30px; }
    </style>
</head>
<body>
    <h1>AI-Powered Video CV Generator</h1>
    <div class="upload-area" id="uploadArea">
        <input type="file" id="videoFile" accept="video/*" required>
        <p>Upload a 30-second video introducing yourself</p>
        <button onclick="processVideo()">Generate CV</button>
    </div>
    <div class="processing" id="processing">
        <p>Processing your video... This may take a few minutes.</p>
        <div class="progress-bar"></div>
    </div>
    <div class="results" id="results"></div>

    <script src="script.js"></script>
</body>
</html>

```

**Subtask 1.2.2: Client-Side JavaScript Logic**
Create `public/script.js` to handle the upload and display process:

```jsx
async function processVideo() {
    const fileInput = document.getElementById('videoFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please select a video file');
        return;
    }

    // Show processing indicator
    document.getElementById('processing').style.display = 'block';

    // Create form data for upload
    const formData = new FormData();
    formData.append('video', file);

    try {
        const response = await fetch('/api/process-video', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        displayResults(result);
    } catch (error) {
        console.error('Error processing video:', error);
        alert('Error processing video. Please try again.');
    } finally {
        document.getElementById('processing').style.display = 'none';
    }
}

function displayResults(data) {
    const resultsDiv = document.getElementById('results');
    // Display the generated CV and analysis results
    resultsDiv.innerHTML = `
        <h2>Generated CV</h2>
        <div class="cv-preview">${data.cv_html}</div>
        <button onclick="downloadPDF('${data.pdf_url}')">Download PDF</button>
        <h3>Analysis Summary</h3>
        <div class="analysis">${data.analysis_summary}</div>
    `;
}

```

## Phase 2: Video Processing Pipeline Development

### Task 2.1: Video Upload and Validation System

**Subtask 2.1.1: File Upload Middleware Configuration**
Create `src/middleware/upload.js` to handle video file uploads safely:

```jsx
const multer = require('multer');
const path = require('path');

// Configure storage settings for uploaded videos
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, process.env.UPLOAD_DIR || 'uploads');
    },
    filename: (req, file, cb) => {
        // Generate unique filename to prevent conflicts
        const uniqueName = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueName + path.extname(file.originalname));
    }
});

// File validation to ensure we only accept video files
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/webm'];

    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Only video files are allowed'), false);
    }
};

// Configure upload limits and validation
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit for video files
    }
});

module.exports = upload;

```

**Subtask 2.1.2: Video Metadata Extraction**
Create `src/services/videoProcessor.js` to extract basic video information:

```jsx
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');

class VideoProcessor {

    // Extract basic metadata from uploaded video
    async getVideoMetadata(videoPath) {
        return new Promise((resolve, reject) => {
            ffmpeg.ffprobe(videoPath, (err, metadata) => {
                if (err) {
                    reject(new Error(`Failed to read video metadata: ${err.message}`));
                    return;
                }

                const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
                const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

                resolve({
                    duration: metadata.format.duration,
                    size: metadata.format.size,
                    hasAudio: !!audioStream,
                    hasVideo: !!videoStream,
                    width: videoStream?.width,
                    height: videoStream?.height,
                    fps: videoStream?.r_frame_rate
                });
            });
        });
    }

    // Validate that video meets our requirements
    validateVideo(metadata) {
        const errors = [];

        // Check duration (should be around 30 seconds, allow some flexibility)
        if (metadata.duration < 15 || metadata.duration > 45) {
            errors.push('Video should be approximately 30 seconds long');
        }

        // Ensure both audio and video streams exist
        if (!metadata.hasAudio) {
            errors.push('Video must contain audio for speech analysis');
        }

        if (!metadata.hasVideo) {
            errors.push('File must contain video stream');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

module.exports = new VideoProcessor();

```

### Task 2.2: Audio Extraction and Processing

**Subtask 2.2.1: Audio Stream Extraction**
Extend the VideoProcessor class to extract audio from video files:

```jsx
// Add this method to the VideoProcessor class
async extractAudio(videoPath) {
    const audioPath = videoPath.replace(path.extname(videoPath), '_audio.wav');

    return new Promise((resolve, reject) => {
        ffmpeg(videoPath)
            .toFormat('wav')
            .audioCodec('pcm_s16le')
            .audioFrequency(16000) // 16kHz sample rate for speech recognition
            .audioChannels(1) // Mono audio for speech processing
            .on('end', () => {
                console.log('Audio extraction completed');
                resolve(audioPath);
            })
            .on('error', (err) => {
                console.error('Audio extraction failed:', err);
                reject(new Error(`Audio extraction failed: ${err.message}`));
            })
            .save(audioPath);
    });
}

```

**Subtask 2.2.2: Google Speech-to-Text Integration**
Create `src/services/speechToText.js` for transcription:

```jsx
const speech = require('@google-cloud/speech');
const fs = require('fs');

class SpeechToTextService {
    constructor() {
        // Initialize Google Cloud Speech client
        this.client = new speech.SpeechClient();
    }

    async transcribeAudio(audioPath) {
        try {
            // Read the audio file
            const audioBytes = fs.readFileSync(audioPath).toString('base64');

            // Configure the request for optimal speech recognition
            const request = {
                audio: {
                    content: audioBytes,
                },
                config: {
                    encoding: 'LINEAR16',
                    sampleRateHertz: 16000,
                    languageCode: 'en-US',
                    // Enable automatic punctuation for better transcript quality
                    enableAutomaticPunctuation: true,
                    // Use enhanced model for better accuracy
                    useEnhanced: true,
                    // Enable word-level timestamps for advanced analysis
                    enableWordTimeOffsets: true,
                },
            };

            // Perform the transcription
            const [response] = await this.client.recognize(request);

            if (!response.results || response.results.length === 0) {
                throw new Error('No speech detected in audio');
            }

            // Extract transcript and confidence scores
            const transcription = response.results
                .map(result => result.alternatives[0].transcript)
                .join(' ');

            const confidence = response.results
                .reduce((avg, result) => avg + result.alternatives[0].confidence, 0)
                / response.results.length;

            // Extract word-level timing information for speech analysis
            const wordTimings = response.results
                .flatMap(result => result.alternatives[0].words || [])
                .map(word => ({
                    word: word.word,
                    startTime: word.startTime,
                    endTime: word.endTime
                }));

            return {
                transcript: transcription,
                confidence: confidence,
                wordTimings: wordTimings,
                speechDuration: wordTimings.length > 0 ?
                    parseFloat(wordTimings[wordTimings.length - 1].endTime.seconds) : 0
            };

        } catch (error) {
            console.error('Speech transcription failed:', error);
            throw new Error(`Speech transcription failed: ${error.message}`);
        }
    }

    // Analyze speech patterns for presentation skills assessment
    analyzeSpeechPatterns(wordTimings) {
        if (wordTimings.length === 0) return {};

        // Calculate speech rate (words per minute)
        const totalDuration = parseFloat(wordTimings[wordTimings.length - 1].endTime.seconds);
        const wordsPerMinute = (wordTimings.length / totalDuration) * 60;

        // Detect pauses (gaps between words)
        const pauses = [];
        for (let i = 1; i < wordTimings.length; i++) {
            const previousEnd = parseFloat(wordTimings[i-1].endTime.seconds);
            const currentStart = parseFloat(wordTimings[i].startTime.seconds);
            const pauseDuration = currentStart - previousEnd;

            if (pauseDuration > 0.5) { // Pauses longer than 0.5 seconds
                pauses.push(pauseDuration);
            }
        }

        return {
            wordsPerMinute: Math.round(wordsPerMinute),
            averagePauseLength: pauses.length > 0 ?
                pauses.reduce((a, b) => a + b, 0) / pauses.length : 0,
            totalPauses: pauses.length,
            speechFluency: this.calculateFluencyScore(wordsPerMinute, pauses)
        };
    }

    calculateFluencyScore(wpm, pauses) {
        // Optimal speaking rate is around 150-160 WPM for presentations
        let score = 100;

        if (wpm < 120) score -= 20; // Too slow
        else if (wpm > 200) score -= 30; // Too fast

        // Penalize excessive pauses
        if (pauses.length > 5) score -= (pauses.length - 5) * 5;

        return Math.max(0, Math.min(100, score));
    }
}

module.exports = new SpeechToTextService();

```

## Phase 3: Computer Vision Integration

### Task 3.1: Google Vision API Setup for Visual Analysis

**Subtask 3.1.1: Frame Extraction from Video**
Extend VideoProcessor to extract key frames for analysis:

```jsx
// Add to VideoProcessor class
async extractKeyFrames(videoPath, numFrames = 5) {
    const frameDir = path.join(path.dirname(videoPath), 'frames');

    // Create frames directory
    if (!fs.existsSync(frameDir)) {
        fs.mkdirSync(frameDir, { recursive: true });
    }

    return new Promise((resolve, reject) => {
        const frames = [];

        ffmpeg(videoPath)
            .on('end', () => {
                console.log(`Extracted ${frames.length} frames`);
                resolve(frames);
            })
            .on('error', (err) => {
                reject(new Error(`Frame extraction failed: ${err.message}`));
            })
            .on('filenames', (filenames) => {
                // Store frame paths for later processing
                frames.push(...filenames.map(name => path.join(frameDir, name)));
            })
            .screenshots({
                count: numFrames,
                folder: frameDir,
                filename: 'frame-%03d.jpg',
                size: '1280x720' // HD resolution for better analysis
            });
    });
}

```

**Subtask 3.1.2: Comprehensive Visual Analysis Service**
Create `src/services/visionAnalysis.js` for computer vision processing:

```jsx
const vision = require('@google-cloud/vision');
const fs = require('fs');

class VisionAnalysisService {
    constructor() {
        this.client = new vision.ImageAnnotatorClient();
    }

    async analyzeFrames(framePaths) {
        const analysisResults = [];

        // Process each frame to build comprehensive visual profile
        for (const framePath of framePaths) {
            try {
                const frameAnalysis = await this.analyzeSingleFrame(framePath);
                analysisResults.push(frameAnalysis);
            } catch (error) {
                console.error(`Failed to analyze frame ${framePath}:`, error);
                // Continue with other frames even if one fails
            }
        }

        // Aggregate results across all frames
        return this.aggregateFrameAnalysis(analysisResults);
    }

    async analyzeSingleFrame(framePath) {
        const image = fs.readFileSync(framePath);

        // Perform multiple types of analysis simultaneously
        const [
            faceResults,
            textResults,
            objectResults,
            labelResults
        ] = await Promise.all([
            this.client.faceDetection({ image }),
            this.client.textDetection({ image }),
            this.client.objectLocalization({ image }),
            this.client.labelDetection({ image })
        ]);

        return {
            faces: this.processFaceData(faceResults[0]),
            text: this.processTextData(textResults[0]),
            objects: this.processObjectData(objectResults[0]),
            labels: this.processLabelData(labelResults[0]),
            timestamp: Date.now()
        };
    }

    processFaceData(faceResult) {
        if (!faceResult.faceAnnotations || faceResult.faceAnnotations.length === 0) {
            return { detected: false };
        }

        const face = faceResult.faceAnnotations[0]; // Assume single person

        // Analyze facial expressions and presentation quality
        return {
            detected: true,
            confidence: face.detectionConfidence,
            // Emotional state analysis
            joy: this.getLikelihoodScore(face.joyLikelihood),
            confidence_expression: this.getLikelihoodScore(face.surpriseLikelihood),
            anger: this.getLikelihoodScore(face.angerLikelihood),

            // Professional presentation indicators
            eyeContact: this.assessEyeContact(face),
            facePosition: this.assessFacePosition(face.boundingPoly),
            headPose: {
                pan: face.panAngle,
                tilt: face.tiltAngle,
                roll: face.rollAngle
            },

            // Professional appearance indicators
            professionalScore: this.calculateProfessionalScore(face)
        };
    }

    processTextData(textResult) {
        if (!textResult.textAnnotations || textResult.textAnnotations.length === 0) {
            return { detectedText: [] };
        }

        // Extract any visible text (certificates, company names, etc.)
        const detectedTexts = textResult.textAnnotations.map(annotation => ({
            text: annotation.description,
            confidence: annotation.confidence,
            position: annotation.boundingPoly
        }));

        // Look for professional keywords in detected text
        const professionalKeywords = this.extractProfessionalKeywords(detectedTexts);

        return {
            detectedText: detectedTexts,
            professionalKeywords: professionalKeywords,
            hasRelevantText: professionalKeywords.length > 0
        };
    }

    processObjectData(objectResult) {
        if (!objectResult.localizedObjectAnnotations) {
            return { objects: [] };
        }

        // Analyze objects for professional context
        const objects = objectResult.localizedObjectAnnotations.map(obj => ({
            name: obj.name,
            confidence: obj.score,
            position: obj.boundingPoly
        }));

        // Categorize objects by professional relevance
        const professionalObjects = objects.filter(obj =>
            this.isProfessionalObject(obj.name)
        );

        return {
            objects: objects,
            professionalObjects: professionalObjects,
            backgroundScore: this.assessBackground(objects)
        };
    }

    processLabelData(labelResult) {
        if (!labelResult.labelAnnotations) {
            return { labels: [] };
        }

        const labels = labelResult.labelAnnotations.map(label => ({
            description: label.description,
            score: label.score,
            topicality: label.topicality
        }));

        return {
            labels: labels,
            professionalContext: this.assessProfessionalContext(labels)
        };
    }

    // Helper methods for analysis
    getLikelihoodScore(likelihood) {
        const scores = {
            'VERY_UNLIKELY': 0,
            'UNLIKELY': 25,
            'POSSIBLE': 50,
            'LIKELY': 75,
            'VERY_LIKELY': 100
        };
        return scores[likelihood] || 0;
    }

    assessEyeContact(face) {
        // Analyze if person is looking at camera
        const panAngle = Math.abs(face.panAngle || 0);
        const tiltAngle = Math.abs(face.tiltAngle || 0);

        // Good eye contact means minimal head movement
        if (panAngle < 15 && tiltAngle < 15) {
            return { score: 85, assessment: 'Good eye contact' };
        } else if (panAngle < 30 && tiltAngle < 30) {
            return { score: 60, assessment: 'Moderate eye contact' };
        } else {
            return { score: 30, assessment: 'Poor eye contact' };
        }
    }

    assessFacePosition(boundingPoly) {
        // Analyze if face is well-centered and appropriately sized
        // This is a simplified assessment - in production you'd want more sophisticated analysis
        return {
            centered: true, // Placeholder - implement actual centering logic
            appropriateSize: true, // Placeholder - implement size assessment
            score: 75
        };
    }

    calculateProfessionalScore(face) {
        let score = 50; // Base score

        // Positive indicators
        if (this.getLikelihoodScore(face.joyLikelihood) > 50) score += 20;
        if (this.getLikelihoodScore(face.angerLikelihood) < 25) score += 15;

        // Negative indicators
        if (this.getLikelihoodScore(face.angerLikelihood) > 50) score -= 30;

        return Math.max(0, Math.min(100, score));
    }

    extractProfessionalKeywords(detectedTexts) {
        const keywords = ['Certificate', 'Degree', 'Award', 'Company', 'LinkedIn', 'Resume'];
        const found = [];

        detectedTexts.forEach(textItem => {
            keywords.forEach(keyword => {
                if (textItem.text.toLowerCase().includes(keyword.toLowerCase())) {
                    found.push(keyword);
                }
            });
        });

        return [...new Set(found)]; // Remove duplicates
    }

    isProfessionalObject(objectName) {
        const professionalObjects = [
            'Computer', 'Laptop', 'Book', 'Certificate', 'Office',
            'Suit', 'Tie', 'Whiteboard', 'Presentation'
        ];
        return professionalObjects.some(prof =>
            objectName.toLowerCase().includes(prof.toLowerCase())
        );
    }

    assessBackground(objects) {
        // Analyze background for professionalism
        const professionalObjects = objects.filter(obj => this.isProfessionalObject(obj.name));

        if (professionalObjects.length >= 2) {
            return { score: 85, assessment: 'Professional background' };
        } else if (professionalObjects.length === 1) {
            return { score: 60, assessment: 'Moderately professional background' };
        } else {
            return { score: 30, assessment: 'Casual background' };
        }
    }

    assessProfessionalContext(labels) {
        const professionalLabels = ['Office', 'Business', 'Professional', 'Formal', 'Corporate'];
        const contextScore = labels
            .filter(label => professionalLabels.some(prof =>
                label.description.toLowerCase().includes(prof.toLowerCase())))
            .reduce((sum, label) => sum + label.score, 0);

        return {
            score: Math.min(100, contextScore * 100),
            isProfessional: contextScore > 0.3
        };
    }

    // Aggregate analysis across all frames
    aggregateFrameAnalysis(frameAnalyses) {
        const validFrames = frameAnalyses.filter(frame => frame.faces.detected);

        if (validFrames.length === 0) {
            return { error: 'No faces detected in video' };
        }

        // Calculate average scores across frames
        const avgConfidence = validFrames.reduce((sum, frame) =>
            sum + frame.faces.confidence, 0) / validFrames.length;

        const avgJoy = validFrames.reduce((sum, frame) =>
            sum + frame.faces.joy, 0) / validFrames.length;

        const avgEyeContact = validFrames.reduce((sum, frame) =>
            sum + frame.faces.eyeContact.score, 0) / validFrames.length;

        // Aggregate text findings
        const allDetectedText = frameAnalyses.flatMap(frame => frame.text.detectedText);
        const allKeywords = [...new Set(frameAnalyses.flatMap(frame => frame.text.professionalKeywords))];

        // Aggregate object analysis
        const backgroundScores = frameAnalyses.map(frame => frame.objects.backgroundScore.score);
        const avgBackgroundScore = backgroundScores.reduce((a, b) => a + b, 0) / backgroundScores.length;

        return {
            summary: {
                facesDetected: validFrames.length,
                averageConfidence: avgConfidence,
                presentationQuality: {
                    eyeContact: { score: avgEyeContact, assessment: this.getEyeContactAssessment(avgEyeContact) },
                    enthusiasm: { score: avgJoy, assessment: this.getEnthusiasmAssessment(avgJoy) },
                    background: { score: avgBackgroundScore, assessment: this.getBackgroundAssessment(avgBackgroundScore) }
                }
            },
            textAnalysis: {
                detectedText: allDetectedText,
                professionalKeywords: allKeywords,
                hasCredentials: allKeywords.some(k => ['Certificate', 'Degree', 'Award'].includes(k))
            },
            overallProfessionalScore: this.calculateOverallScore(avgEyeContact, avgJoy, avgBackgroundScore)
        };
    }

    getEyeContactAssessment(score) {
        if (score >= 80) return 'Excellent eye contact';
        if (score >= 60) return 'Good eye contact';
        if (score >= 40) return 'Fair eye contact';
        return 'Needs improvement';
    }

    getEnthusiasmAssessment(score) {
        if (score >= 70) return 'Very enthusiastic';
        if (score >= 50) return 'Moderately enthusiastic';
        if (score >= 30) return 'Somewhat reserved';
        return 'Very reserved';
    }

    getBackgroundAssessment(score) {
        if (score >= 80) return 'Professional setting';
        if (score >= 60) return 'Business casual setting';
        if (score >= 40) return 'Neutral setting';
        return 'Casual setting';
    }

    calculateOverallScore(eyeContact, enthusiasm, background) {
        // Weighted average with eye contact being most important
        const weightedScore = (eyeContact * 0.4) + (enthusiasm * 0.3) + (background * 0.3);
        return Math.round(weightedScore);
    }
}

module.exports = new VisionAnalysisService();

```

## Phase 4: AI-Powered Information Extraction

### Task 4.1: Gemini API Integration for Content Analysis

**Subtask 4.1.1: Gemini Service Setup**
Create `src/services/geminiService.js` for intelligent content extraction:

```jsx
const axios = require('axios');

class GeminiService {
    constructor() {
        this.apiKey = process.env.GEMINI_API_KEY;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
    }

    async extractCVInformation(transcript, visualAnalysis) {
        const prompt = this.buildExtractionPrompt(transcript, visualAnalysis);

        try {
            const response = await axios.post(`${this.baseUrl}?key=${this.apiKey}`, {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1, // Low temperature for factual extraction
                    topK: 1,
                    topP: 0.8,
                    maxOutputTokens: 2048,
                }
            });

            const generatedText = response.data.candidates[0].content.parts[0].text;
            return this.parseStructuredResponse(generatedText);

        } catch (error) {
            console.error('Gemini API error:', error);
            throw new Error(`CV extraction failed: ${error.message}`);
        }
    }

    buildExtractionPrompt(transcript, visualAnalysis) {
        return `
You are an expert HR professional analyzing a candidate's video introduction for CV generation.

TRANSCRIPT OF SPOKEN CONTENT:
"${transcript}"

VISUAL ANALYSIS DATA:
- Overall Professional Score: ${visualAnalysis.overallProfessionalScore}/100
- Eye Contact Quality: ${visualAnalysis.summary.presentationQuality.eyeContact.assessment}
- Background Setting: ${visualAnalysis.summary.presentationQuality.background.assessment}
- Enthusiasm Level: ${visualAnalysis.summary.presentationQuality.enthusiasm.assessment}
- Professional Keywords Detected: ${visualAnalysis.textAnalysis.professionalKeywords.join(', ') || 'None'}
- Credentials Visible: ${visualAnalysis.textAnalysis.hasCredentials ? 'Yes' : 'No'}

TASK: Extract structured CV information from the transcript and visual cues. Pay attention to:

1. PERSONAL INFORMATION:
   - Name (if mentioned)
   - Contact information (email, phone, LinkedIn if mentioned)
   - Location (if mentioned)

2. PROFESSIONAL SUMMARY:
   - Current role/title
   - Years of experience
   - Key professional strengths
   - Career objectives

3. SKILLS AND EXPERTISE:
   - Technical skills mentioned
   - Soft skills demonstrated or mentioned
   - Programming languages, tools, technologies
   - Industry-specific competencies

4. WORK EXPERIENCE:
   - Current position
   - Previous roles (if mentioned)
   - Companies worked for
   - Key achievements or projects
   - Responsibilities mentioned

5. EDUCATION:
   - Degrees mentioned
   - Institutions
   - Certifications or courses
   - Relevant academic achievements

6. PRESENTATION SKILLS ASSESSMENT:
   - Communication clarity
   - Professional demeanor
   - Confidence level
   - Overall presentation quality
```

```jsx
7. ADDITIONAL QUALIFICATIONS:
   - Languages spoken
   - Volunteer work or leadership roles
   - Awards or recognitions
   - Professional memberships
   - Publications or projects

IMPORTANT INSTRUCTIONS:
- Extract only information explicitly mentioned or clearly implied in the transcript
- Do not invent or assume information not provided
- Use the visual analysis to enhance professional assessment
- If information is not available, mark as "Not specified"
- Be conservative in your extractions - accuracy over completeness

RESPOND ONLY WITH VALID JSON in this exact format:
{
  "personalInfo": {
    "name": "string or null",
    "email": "string or null",
    "phone": "string or null",
    "linkedin": "string or null",
    "location": "string or null"
  },
  "professionalSummary": {
    "currentRole": "string or null",
    "experienceYears": "number or null",
    "keyStrengths": ["array of strings"],
    "careerObjective": "string or null"
  },
  "skills": {
    "technical": ["array of strings"],
    "soft": ["array of strings"],
    "languages": ["array of strings"],
    "tools": ["array of strings"]
  },
  "workExperience": [
    {
      "position": "string",
      "company": "string or null",
      "duration": "string or null",
      "responsibilities": ["array of strings"],
      "achievements": ["array of strings"]
    }
  ],
  "education": [
    {
      "degree": "string",
      "institution": "string or null",
      "year": "string or null",
      "details": "string or null"
    }
  ],
  "presentationAssessment": {
    "communicationClarity": "number (1-10)",
    "professionalDemeanor": "number (1-10)",
    "confidenceLevel": "number (1-10)",
    "overallScore": "number (1-10)",
    "strengths": ["array of strings"],
    "improvements": ["array of strings"]
  },
  "additionalInfo": {
    "certifications": ["array of strings"],
    "projects": ["array of strings"],
    "awards": ["array of strings"],
    "volunteering": ["array of strings"],
    "interests": ["array of strings"]
  }
}

DO NOT include any text outside of the JSON structure. The response must be parseable JSON only.
`;
    }

    parseStructuredResponse(generatedText) {
        try {
            // Clean up the response to extract only JSON
            const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No valid JSON found in response');
            }

            const jsonStr = jsonMatch[0];
            return JSON.parse(jsonStr);

        } catch (error) {
            console.error('Failed to parse Gemini response:', error);
            throw new Error('Invalid response format from AI service');
        }
    }

    async enhanceCV(cvData, additionalContext = {}) {
        const enhancementPrompt = `
Given this extracted CV data:
${JSON.stringify(cvData, null, 2)}

Additional Context:
- Video duration: ${additionalContext.videoDuration || 'Unknown'}
- Speech fluency score: ${additionalContext.speechFluency || 'Unknown'}
- Professional setting score: ${additionalContext.backgroundScore || 'Unknown'}

TASK: Enhance and refine the CV data by:
1. Improving descriptions and summaries
2. Adding professional formatting suggestions
3. Identifying potential gaps or areas for improvement
4. Providing industry-specific recommendations

Respond with enhanced JSON in the same structure, with improved content and an additional "recommendations" section.
        `;

        try {
            const response = await axios.post(`${this.baseUrl}?key=${this.apiKey}`, {
                contents: [{
                    parts: [{
                        text: enhancementPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 40,
                    topP: 0.8,
                    maxOutputTokens: 2048,
                }
            });

            const generatedText = response.data.candidates[0].content.parts[0].text;
            return this.parseStructuredResponse(generatedText);

        } catch (error) {
            console.error('CV enhancement failed:', error);
            // Return original data if enhancement fails
            return cvData;
        }
    }
}

module.exports = new GeminiService();

```

**Subtask 4.1.2: Content Validation and Sanitization**
Create `src/services/contentValidator.js`:

```jsx
class ContentValidator {

    validateCVData(cvData) {
        const validation = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // Validate required sections
        if (!cvData.personalInfo && !cvData.professionalSummary) {
            validation.errors.push('Insufficient personal or professional information extracted');
            validation.isValid = false;
        }

        // Check for suspicious or inappropriate content
        const inappropriateWords = ['inappropriate', 'offensive']; // Add more as needed
        const allText = JSON.stringify(cvData).toLowerCase();

        inappropriateWords.forEach(word => {
            if (allText.includes(word)) {
                validation.warnings.push(`Potentially inappropriate content detected: ${word}`);
            }
        });

        // Validate data formats
        if (cvData.personalInfo?.email && !this.isValidEmail(cvData.personalInfo.email)) {
            validation.warnings.push('Email format appears invalid');
        }

        // Check for completeness
        if (!cvData.skills?.technical?.length && !cvData.skills?.soft?.length) {
            validation.warnings.push('No skills detected - consider prompting candidate to mention specific skills');
        }

        return validation;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    sanitizeCVData(cvData) {
        // Remove any potentially harmful content
        // This is a basic implementation - expand based on needs

        const sanitized = JSON.parse(JSON.stringify(cvData)); // Deep clone

        // Remove any script tags or HTML content
        const cleanText = (text) => {
            if (typeof text !== 'string') return text;
            return text.replace(/<[^>]*>/g, '').trim();
        };

        // Recursively clean all string values
        const cleanObject = (obj) => {
            if (Array.isArray(obj)) {
                return obj.map(item => typeof item === 'string' ? cleanText(item) : cleanObject(item));
            } else if (obj && typeof obj === 'object') {
                const cleaned = {};
                for (const [key, value] of Object.entries(obj)) {
                    cleaned[key] = typeof value === 'string' ? cleanText(value) : cleanObject(value);
                }
                return cleaned;
            }
            return obj;
        };

        return cleanObject(sanitized);
    }
}

module.exports = new ContentValidator();

```

## Phase 5: CV Generation and Output

### Task 5.1: CV Template System

**Subtask 5.1.1: HTML CV Template Creation**
Create `src/templates/cvTemplate.js`:

```jsx
class CVTemplate {

    generateModernCV(cvData) {
        const personalInfo = cvData.personalInfo || {};
        const professionalSummary = cvData.professionalSummary || {};
        const skills = cvData.skills || {};
        const workExperience = cvData.workExperience || [];
        const education = cvData.education || [];
        const presentationAssessment = cvData.presentationAssessment || {};
        const additionalInfo = cvData.additionalInfo || {};

        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${personalInfo.name || 'Professional Resume'}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .cv-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header h2 {
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
            font-weight: 300;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h3 {
            color: #667eea;
            font-size: 1.4em;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .professional-summary {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-bottom: 30px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .skill-category {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #eee;
        }

        .skill-category h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .skill-tag {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .experience-item, .education-item {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .experience-header, .education-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .experience-title, .education-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }

        .experience-company, .education-institution {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .experience-duration, .education-year {
            color: #666;
            font-size: 0.9em;
            font-weight: 500;
            background: white;
            padding: 5px 10px;
            border-radius: 4px;
        }

        .experience-list, .education-details {
            list-style: none;
            padding-left: 0;
        }

        .experience-list li, .education-details li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .experience-list li:before {
            content: "▸";
            color: #667eea;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .presentation-score {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }

        .score-item {
            display: inline-block;
            margin: 0 15px;
            text-align: center;
        }

        .score-value {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .score-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .ai-generated-note {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-top: 30px;
            text-align: center;
            font-size: 0.9em;
            color: #1976d2;
        }

        @media print {
            body { background: white; }
            .cv-container { box-shadow: none; margin: 0; }
            .ai-generated-note { display: none; }
        }

        @media (max-width: 768px) {
            .contact-info { flex-direction: column; align-items: center; }
            .experience-header, .education-header { flex-direction: column; align-items: flex-start; }
            .skills-grid { grid-template-columns: 1fr; }
            .content { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header Section -->
        <div class="header">
            <h1>${personalInfo.name || 'Professional Candidate'}</h1>
            <h2>${professionalSummary.currentRole || 'Seeking Professional Opportunities'}</h2>

            <div class="contact-info">
                ${personalInfo.email ? `<div class="contact-item">📧 ${personalInfo.email}</div>` : ''}
                ${personalInfo.phone ? `<div class="contact-item">📱 ${personalInfo.phone}</div>` : ''}
                ${personalInfo.linkedin ? `<div class="contact-item">💼 ${personalInfo.linkedin}</div>` : ''}
                ${personalInfo.location ? `<div class="contact-item">📍 ${personalInfo.location}</div>` : ''}
            </div>
        </div>

        <!-- Content Section -->
        <div class="content">
            ${this.generatePresentationAssessment(presentationAssessment)}
            ${this.generateProfessionalSummary(professionalSummary)}
            ${this.generateSkillsSection(skills)}
            ${this.generateExperienceSection(workExperience)}
            ${this.generateEducationSection(education)}
            ${this.generateAdditionalSection(additionalInfo)}

            <div class="ai-generated-note">
                🤖 This CV was generated using AI analysis of a 30-second video introduction.
                The information has been extracted and formatted automatically.
                Please review and verify all details for accuracy.
            </div>
        </div>
    </div>
</body>
</html>`;
    }

    generatePresentationAssessment(assessment) {
        if (!assessment || Object.keys(assessment).length === 0) return '';

        return `
        <div class="section">
            <h3>Presentation Assessment</h3>
            <div class="presentation-score">
                <div class="score-item">
                    <span class="score-value">${assessment.overallScore || 'N/A'}/10</span>
                    <span class="score-label">Overall Score</span>
                </div>
                <div class="score-item">
                    <span class="score-value">${assessment.communicationClarity || 'N/A'}/10</span>
                    <span class="score-label">Communication</span>
                </div>
                <div class="score-item">
                    <span class="score-value">${assessment.confidenceLevel || 'N/A'}/10</span>
                    <span class="score-label">Confidence</span>
                </div>
                <div class="score-item">
                    <span class="score-value">${assessment.professionalDemeanor || 'N/A'}/10</span>
                    <span class="score-label">Professionalism</span>
                </div>
            </div>

            ${assessment.strengths && assessment.strengths.length > 0 ? `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #4caf50; margin-bottom: 10px;">Presentation Strengths:</h4>
                <div class="skill-tags">
                    ${assessment.strengths.map(strength => `<span class="skill-tag" style="background: #4caf50;">${strength}</span>`).join('')}
                </div>
            </div>
            ` : ''}

            ${assessment.improvements && assessment.improvements.length > 0 ? `
            <div>
                <h4 style="color: #ff9800; margin-bottom: 10px;">Areas for Improvement:</h4>
                <div class="skill-tags">
                    ${assessment.improvements.map(improvement => `<span class="skill-tag" style="background: #ff9800;">${improvement}</span>`).join('')}
                </div>
            </div>
            ` : ''}
        </div>`;
    }

    generateProfessionalSummary(summary) {
        if (!summary || Object.keys(summary).length === 0) return '';

        const summaryParts = [];

        if (summary.experienceYears) {
            summaryParts.push(`${summary.experienceYears} years of professional experience`);
        }

        if (summary.keyStrengths && summary.keyStrengths.length > 0) {
            summaryParts.push(`Key strengths in ${summary.keyStrengths.join(', ')}`);
        }

        const summaryText = summary.careerObjective || summaryParts.join('. ') || 'Professional seeking new opportunities.';

        return `
        <div class="section">
            <div class="professional-summary">
                <h3>Professional Summary</h3>
                <p>${summaryText}</p>
            </div>
        </div>`;
    }

    generateSkillsSection(skills) {
        if (!skills || ((!skills.technical || skills.technical.length === 0) &&
                      (!skills.soft || skills.soft.length === 0) &&
                      (!skills.languages || skills.languages.length === 0) &&
                      (!skills.tools || skills.tools.length === 0))) {
            return '';
        }

        return `
        <div class="section">
            <h3>Skills & Expertise</h3>
            <div class="skills-grid">
                ${skills.technical && skills.technical.length > 0 ? `
                <div class="skill-category">
                    <h4>Technical Skills</h4>
                    <div class="skill-tags">
                        ${skills.technical.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                    </div>
                </div>` : ''}

                ${skills.soft && skills.soft.length > 0 ? `
                <div class="skill-category">
                    <h4>Soft Skills</h4>
                    <div class="skill-tags">
                        ${skills.soft.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                    </div>
                </div>` : ''}

                ${skills.tools && skills.tools.length > 0 ? `
                <div class="skill-category">
                    <h4>Tools & Technologies</h4>
                    <div class="skill-tags">
                        ${skills.tools.map(tool => `<span class="skill-tag">${tool}</span>`).join('')}
                    </div>
                </div>` : ''}

                ${skills.languages && skills.languages.length > 0 ? `
                <div class="skill-category">
                    <h4>Languages</h4>
                    <div class="skill-tags">
                        ${skills.languages.map(lang => `<span class="skill-tag">${lang}</span>`).join('')}
                    </div>
                </div>` : ''}
            </div>
        </div>`;
    }

    generateExperienceSection(workExperience) {
        if (!workExperience || workExperience.length === 0) return '';

        return `
        <div class="section">
            <h3>Work Experience</h3>
            ${workExperience.map(exp => `
                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="experience-title">${exp.position || 'Position Not Specified'}</div>
                            ${exp.company ? `<div class="experience-company">${exp.company}</div>` : ''}
                        </div>
                        ${exp.duration ? `<div class="experience-duration">${exp.duration}</div>` : ''}
                    </div>

                    ${exp.responsibilities && exp.responsibilities.length > 0 ? `
                    <div>
                        <h4 style="margin-bottom: 10px; color: #555;">Key Responsibilities:</h4>
                        <ul class="experience-list">
                            ${exp.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
                        </ul>
                    </div>
                    ` : ''}

                    ${exp.achievements && exp.achievements.length > 0 ? `
                    <div style="margin-top: 15px;">
                        <h4 style="margin-bottom: 10px; color: #555;">Key Achievements:</h4>
                        <ul class="experience-list">
                            ${exp.achievements.map(achievement => `<li>${achievement}</li>`).join('')}
                        </ul>
                    </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>`;
    }

    generateEducationSection(education) {
        if (!education || education.length === 0) return '';

        return `
        <div class="section">
            <h3>Education</h3>
            ${education.map(edu => `
                <div class="education-item">
                    <div class="education-header">
                        <div>
                            <div class="education-title">${edu.degree || 'Degree Not Specified'}</div>
                            ${edu.institution ? `<div class="education-institution">${edu.institution}</div>` : ''}
                        </div>
                        ${edu.year ? `<div class="education-year">${edu.year}</div>` : ''}
                    </div>
                    ${edu.details ? `<p style="margin-top: 10px; color: #666;">${edu.details}</p>` : ''}
                </div>
            `).join('')}
        </div>`;
    }

    generateAdditionalSection(additionalInfo) {
        if (!additionalInfo || Object.keys(additionalInfo).length === 0) return '';

        const sections = [];

        if (additionalInfo.certifications && additionalInfo.certifications.length > 0) {
            sections.push({
                title: 'Certifications',
                items: additionalInfo.certifications
            });
        }

        if (additionalInfo.projects && additionalInfo.projects.length > 0) {
            sections.push({
                title: 'Notable Projects',
                items: additionalInfo.projects
            });
        }

        if (additionalInfo.awards && additionalInfo.awards.length > 0) {
            sections.push({
                title: 'Awards & Recognition',
                items: additionalInfo.awards
            });
        }

        if (additionalInfo.volunteering && additionalInfo.volunteering.length > 0) {
            sections.push({
                title: 'Volunteer Experience',
                items: additionalInfo.volunteering
            });
        }

        if (additionalInfo.interests && additionalInfo.interests.length > 0) {
            sections.push({
                title: 'Interests',
                items: additionalInfo.interests
            });
        }

        if (sections.length === 0) return '';

        return `
        <div class="section">
            <h3>Additional Information</h3>
            <div class="skills-grid">
                ${sections.map(section => `
                    <div class="skill-category">
                        <h4>${section.title}</h4>
                        <div class="skill-tags">
                            ${section.items.map(item => `<span class="skill-tag">${item}</span>`).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>`;
    }
}

module.exports = new CVTemplate();

```

**Subtask 5.1.2: PDF Generation Service**
Create `src/services/pdfGenerator.js`:

```jsx
const htmlPdf = require('html-pdf-node');
const path = require('path');
const fs = require('fs');

class PDFGenerator {

    async generatePDF(htmlContent, outputFileName) {
        const options = {
            format: 'A4',
            border: {
                top: '20mm',
                right: '20mm',
                bottom: '20mm',
                left: '20mm'
            },
            printBackground: true,
            displayHeaderFooter: false,
            margin: {
                top: '20mm',
                bottom: '20mm'
            }
        };

        const file = { content: htmlContent };

        try {
            const pdfBuffer = await htmlPdf.generatePdf(file, options);

            // Ensure output directory exists
            const outputDir = process.env.OUTPUT_DIR || 'output';
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            const outputPath = path.join(outputDir, outputFileName);
            fs.writeFileSync(outputPath, pdfBuffer);

            return {
                success: true,
                filePath: outputPath,
                fileName: outputFileName,
                size: pdfBuffer.length
            };

        } catch (error) {
            console.error('PDF generation failed:', error);
            throw new Error(`PDF generation failed: ${error.message}`);
        }
    }

    async generatePreviewImages(htmlContent, outputFileName) {
        // This would generate preview images of the CV
        // Implementation depends on requirements
        // Could use Puppeteer for screenshot generation

        return {
            success: true,
            message: 'Preview generation not implemented yet'
        };
    }
}

module.exports = new PDFGenerator();

```

## Phase 6: API Routes and Main Controller

### Task 6.1: Main Processing Controller

**Subtask 6.1.1: Video Processing Controller**
Create `src/controllers/videoController.js`:

```jsx
const videoProcessor = require('../services/videoProcessor');
const speechToText = require('../services/speechToText');
const visionAnalysis = require('../services/visionAnalysis');
const geminiService = require('../services/geminiService');
const contentValidator = require('../services/contentValidator');
const cvTemplate = require('../templates/cvTemplate');
const pdfGenerator = require('../services/pdfGenerator');
const fs = require('fs');
const path = require('path');

class VideoController {

    async processVideo(req, res) {
        let tempFiles = []; // Track temporary files for cleanup

        try {
            // Validate file upload
            if (!req.file) {
                return res.status(400).json({ error: 'No video file uploaded' });
            }

            const videoPath = req.file.path;
            tempFiles.push(videoPath);

            console.log('Starting video processing pipeline...');

            // Step 1: Validate video metadata
            const metadata = await videoProcessor.getVideoMetadata(videoPath);
            const validation = videoProcessor.validateVideo(metadata);

            if (!validation.isValid) {
                await this.cleanup(tempFiles);
                return res.status(400).json({
                    error: 'Video validation failed',
                    details: validation.errors
                });
            }

            console.log('Video validation passed. Duration:', metadata.duration, 'seconds');

            // Step 2: Extract audio for speech processing
            console.log('Extracting audio from video...');
            const audioPath = await videoProcessor.extractAudio(videoPath);
            tempFiles.push(audioPath);

            // Step 3: Extract frames for visual analysis
            console.log('Extracting frames for visual analysis...');
            const framePaths = await videoProcessor.extractKeyFrames(videoPath);
            tempFiles.push(...framePaths);

            // Step 4: Process audio and visual data in parallel
            console.log('Starting parallel processing of audio and visual data...');
            const [transcriptionResult, visualAnalysisResult] = await Promise.all([
                speechToText.transcribeAudio(audioPath),
                visionAnalysis.analyzeFrames(framePaths)
            ]);

            console.log('Transcription completed. Confidence:', transcriptionResult.confidence);
            console.log('Visual analysis completed. Professional score:', visualAnalysisResult.overallProfessionalScore);

            // Step 5: Analyze speech patterns for presentation assessment
            const speechPatterns = speechToText.analyzeSpeechPatterns(transcriptionResult.wordTimings);

            // Step 6: Extract CV information using AI
            console.log('Extracting CV information using Gemini AI...');
            const cvData = await geminiService.extractCVInformation(
                transcriptionResult.transcript,
                visualAnalysisResult
            );

            // Step 7: Validate and sanitize extracted data
            const dataValidation = contentValidator.validateCVData(cvData);
            const sanitizedCVData = contentValidator.sanitizeCVData(cvData);

            // Step 8: Enhance CV with additional context
            const enhancedCVData = await geminiService.enhanceCV(sanitizedCVData, {
                videoDuration: metadata.duration,
```

```jsx
                speechFluency: speechPatterns.speechFluency,
                backgroundScore: visualAnalysisResult.summary.presentationQuality.background.score,
                eyeContactScore: visualAnalysisResult.summary.presentationQuality.eyeContact.score
            });

            // Step 9: Generate HTML CV
            console.log('Generating CV template...');
            const cvHTML = cvTemplate.generateModernCV(enhancedCVData);

            // Step 10: Generate PDF
            const pdfFileName = `cv_${Date.now()}.pdf`;
            console.log('Generating PDF...');
            const pdfResult = await pdfGenerator.generatePDF(cvHTML, pdfFileName);

            // Step 11: Cleanup temporary files
            await this.cleanup(tempFiles);

            // Step 12: Prepare response with comprehensive results
            const response = {
                success: true,
                cvData: enhancedCVData,
                cv_html: cvHTML,
                pdf_url: `/download/${pdfFileName}`,
                pdf_filename: pdfFileName,
                analysis_summary: this.generateAnalysisSummary(
                    transcriptionResult,
                    visualAnalysisResult,
                    speechPatterns,
                    metadata
                ),
                validation: dataValidation,
                processing_stats: {
                    videoDuration: metadata.duration,
                    transcriptionConfidence: transcriptionResult.confidence,
                    visualAnalysisScore: visualAnalysisResult.overallProfessionalScore,
                    speechFluency: speechPatterns.speechFluency,
                    processingTime: Date.now() - req.startTime
                }
            };

            console.log('Video processing completed successfully');
            res.json(response);

        } catch (error) {
            console.error('Video processing error:', error);

            // Cleanup on error
            await this.cleanup(tempFiles);

            res.status(500).json({
                error: 'Video processing failed',
                message: error.message,
                details: process.env.NODE_ENV === 'development' ? error.stack : undefined
            });
        }
    }

    generateAnalysisSummary(transcription, visualAnalysis, speechPatterns, metadata) {
        const summary = {
            overview: `Analyzed ${Math.round(metadata.duration)}s video with ${transcription.confidence > 0.8 ? 'high' : 'moderate'} speech clarity`,
            speechAnalysis: {
                wordsPerMinute: speechPatterns.wordsPerMinute,
                fluencyScore: speechPatterns.speechFluency,
                confidenceLevel: Math.round(transcription.confidence * 100) + '%'
            },
            visualAnalysis: {
                professionalScore: visualAnalysis.overallProfessionalScore,
                eyeContact: visualAnalysis.summary.presentationQuality.eyeContact.assessment,
                background: visualAnalysis.summary.presentationQuality.background.assessment,
                enthusiasm: visualAnalysis.summary.presentationQuality.enthusiasm.assessment
            },
            recommendations: this.generateRecommendations(transcription, visualAnalysis, speechPatterns)
        };

        return summary;
    }

    generateRecommendations(transcription, visualAnalysis, speechPatterns) {
        const recommendations = [];

        // Speech-based recommendations
        if (speechPatterns.wordsPerMinute < 120) {
            recommendations.push("Consider speaking slightly faster to maintain engagement");
        } else if (speechPatterns.wordsPerMinute > 200) {
            recommendations.push("Try slowing down speech pace for better clarity");
        }

        if (speechPatterns.totalPauses > 8) {
            recommendations.push("Practice reducing long pauses for more fluent delivery");
        }

        if (transcription.confidence < 0.7) {
            recommendations.push("Focus on clearer articulation and pronunciation");
        }

        // Visual-based recommendations
        if (visualAnalysis.summary.presentationQuality.eyeContact.score < 60) {
            recommendations.push("Maintain better eye contact with the camera");
        }

        if (visualAnalysis.summary.presentationQuality.background.score < 50) {
            recommendations.push("Consider using a more professional background setting");
        }

        if (visualAnalysis.summary.presentationQuality.enthusiasm.score < 40) {
            recommendations.push("Show more enthusiasm and energy in your presentation");
        }

        // Content-based recommendations
        if (!transcription.transcript.toLowerCase().includes('experience')) {
            recommendations.push("Mention specific work experience and achievements");
        }

        if (!transcription.transcript.toLowerCase().match(/skill|technology|tool/)) {
            recommendations.push("Highlight your technical skills and expertise");
        }

        return recommendations;
    }

    async cleanup(filePaths) {
        for (const filePath of filePaths) {
            try {
                if (fs.existsSync(filePath)) {
                    if (fs.lstatSync(filePath).isDirectory()) {
                        fs.rmSync(filePath, { recursive: true, force: true });
                    } else {
                        fs.unlinkSync(filePath);
                    }
                }
            } catch (error) {
                console.error(`Failed to cleanup file ${filePath}:`, error);
            }
        }
    }

    async downloadCV(req, res) {
        try {
            const fileName = req.params.filename;
            const filePath = path.join(process.env.OUTPUT_DIR || 'output', fileName);

            if (!fs.existsSync(filePath)) {
                return res.status(404).json({ error: 'File not found' });
            }

            res.download(filePath, fileName, (err) => {
                if (err) {
                    console.error('Download error:', err);
                    res.status(500).json({ error: 'Download failed' });
                }
            });

        } catch (error) {
            console.error('Download error:', error);
            res.status(500).json({ error: 'Download failed' });
        }
    }
}

module.exports = new VideoController();

```

**Subtask 6.1.2: API Routes Configuration**
Create `src/routes/api.js`:

```jsx
const express = require('express');
const upload = require('../middleware/upload');
const videoController = require('../controllers/videoController');

const router = express.Router();

// Middleware to track processing time
router.use((req, res, next) => {
    req.startTime = Date.now();
    next();
});

// Video processing endpoint
router.post('/process-video', upload.single('video'), videoController.processVideo);

// Download generated CV
router.get('/download/:filename', videoController.downloadCV);

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
            server: 'running',
            uploads: 'enabled',
            ai_services: 'connected'
        }
    });
});

// Get supported video formats
router.get('/supported-formats', (req, res) => {
    res.json({
        formats: ['mp4', 'avi', 'mov', 'webm'],
        maxSize: '100MB',
        recommendedDuration: '30 seconds',
        requirements: [
            'Video must contain both audio and video streams',
            'Clear speech for accurate transcription',
            'Visible face for presentation analysis',
            'Good lighting and audio quality recommended'
        ]
    });
});

module.exports = router;

```

### Task 6.2: Main Application Setup

**Subtask 6.2.1: Express Server Configuration**
Create `src/app.js`:

```jsx
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import routes
const apiRoutes = require('./routes/api');

class Application {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
        this.ensureDirectories();
    }

    setupMiddleware() {
        // Enable CORS for frontend integration
        this.app.use(cors({
            origin: process.env.FRONTEND_URL || '*',
            credentials: true
        }));

        // Parse JSON requests
        this.app.use(express.json({ limit: '50mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

        // Serve static files
        this.app.use(express.static('public'));

        // Request logging middleware
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });

        // Security headers
        this.app.use((req, res, next) => {
            res.setHeader('X-Content-Type-Options', 'nosniff');
            res.setHeader('X-Frame-Options', 'DENY');
            res.setHeader('X-XSS-Protection', '1; mode=block');
            next();
        });
    }

    setupRoutes() {
        // API routes
        this.app.use('/api', apiRoutes);

        // Root endpoint
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../public/index.html'));
        });

        // Catch-all route for SPA support
        this.app.get('*', (req, res) => {
            // Don't catch API routes or file requests
            if (req.path.startsWith('/api/') || req.path.includes('.')) {
                res.status(404).json({ error: 'Endpoint not found' });
            } else {
                res.sendFile(path.join(__dirname, '../public/index.html'));
            }
        });
    }

    setupErrorHandling() {
        // Handle multer errors (file upload issues)
        this.app.use((error, req, res, next) => {
            if (error.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({
                    error: 'File too large',
                    message: 'Video file must be smaller than 100MB'
                });
            }

            if (error.code === 'LIMIT_UNEXPECTED_FILE') {
                return res.status(400).json({
                    error: 'Invalid file',
                    message: 'Only video files are allowed'
                });
            }

            next(error);
        });

        // Global error handler
        this.app.use((error, req, res, next) => {
            console.error('Unhandled error:', error);

            res.status(500).json({
                error: 'Internal server error',
                message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        });

        // Handle 404s
        this.app.use((req, res) => {
            res.status(404).json({
                error: 'Not found',
                path: req.path,
                method: req.method
            });
        });
    }

    ensureDirectories() {
        // Create necessary directories if they don't exist
        const directories = [
            process.env.UPLOAD_DIR || 'uploads',
            process.env.OUTPUT_DIR || 'output',
            'public'
        ];

        directories.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`Created directory: ${dir}`);
            }
        });
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`🚀 Video CV Generator Server running on port ${this.port}`);
            console.log(`📁 Upload directory: ${process.env.UPLOAD_DIR || 'uploads'}`);
            console.log(`📄 Output directory: ${process.env.OUTPUT_DIR || 'output'}`);
            console.log(`🌐 Access the application at: http://localhost:${this.port}`);

            // Log service status
            console.log('\n🔧 Service Configuration:');
            console.log(`- Google Cloud Services: ${process.env.GOOGLE_APPLICATION_CREDENTIALS ? '✅ Configured' : '❌ Missing credentials'}`);
            console.log(`- Gemini AI: ${process.env.GEMINI_API_KEY ? '✅ Configured' : '❌ Missing API key'}`);
            console.log(`- Environment: ${process.env.NODE_ENV || 'development'}`);
        });

        // Graceful shutdown handling
        process.on('SIGTERM', this.shutdown.bind(this));
        process.on('SIGINT', this.shutdown.bind(this));
    }

    shutdown() {
        console.log('\n🛑 Shutting down server...');

        // Cleanup temporary files
        this.cleanupTempFiles();

        process.exit(0);
    }

    cleanupTempFiles() {
        try {
            const uploadDir = process.env.UPLOAD_DIR || 'uploads';
            if (fs.existsSync(uploadDir)) {
                const files = fs.readdirSync(uploadDir);
                files.forEach(file => {
                    const filePath = path.join(uploadDir, file);
                    fs.unlinkSync(filePath);
                });
                console.log('✅ Temporary files cleaned up');
            }
        } catch (error) {
            console.error('❌ Cleanup error:', error);
        }
    }
}

module.exports = Application;

```

**Subtask 6.2.2: Server Entry Point**
Create `server.js`:

```jsx
#!/usr/bin/env node

const Application = require('./src/app');

// Check for required environment variables
const requiredEnvVars = [
    'GOOGLE_APPLICATION_CREDENTIALS',
    'GEMINI_API_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => {
        console.error(`   - ${varName}`);
    });
    console.error('\n📋 Setup Instructions:');
    console.error('1. Create a Google Cloud service account and download credentials JSON');
    console.error('2. Get a Gemini API key from Google AI Studio');
    console.error('3. Create a .env file with the required variables');
    console.error('\nExample .env file:');
    console.error('GOOGLE_APPLICATION_CREDENTIALS=./path/to/service-account.json');
    console.error('GEMINI_API_KEY=your_gemini_api_key_here');
    console.error('PORT=3000');
    process.exit(1);
}

// Start the application
const app = new Application();
app.start();

```

## Phase 7: Package Configuration and Deployment Setup

### Task 7.1: Package.json and Scripts

**Subtask 7.1.1: Update Package.json**
Update your `package.json` with proper scripts and metadata:

```json
{
  "name": "video-cv-generator",
  "version": "1.0.0",
  "description": "AI-powered CV generation from 30-second video introductions",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "echo \"Testing not implemented yet\" && exit 1",
    "setup": "node scripts/setup.js",
    "cleanup": "node scripts/cleanup.js",
    "check-services": "node scripts/check-services.js"
  },
  "keywords": [
    "cv",
    "resume",
    "ai",
    "video-analysis",
    "computer-vision",
    "speech-recognition",
    "job-application"
  ],
  "author": "Your Name",
  "license": "MIT",
  "engines": {
    "node": ">=14.0.0"
  },
  "dependencies": {
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "@google-cloud/speech": "^6.0.1",
    "@google-cloud/vision": "^4.0.2",
    "axios": "^1.5.0",
    "form-data": "^4.0.0",
    "fluent-ffmpeg": "^2.1.2",
    "html-pdf-node": "^1.0.8",
    "jspdf": "^2.5.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}

```

**Subtask 7.1.2: Setup and Utility Scripts**
Create `scripts/setup.js`:

```jsx
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Video CV Generator...\n');

// Check Node.js version
const nodeVersion = process.version;
const requiredVersion = '14.0.0';
console.log(`📍 Node.js version: ${nodeVersion}`);

// Create necessary directories
const directories = ['uploads', 'output', 'public'];
directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    } else {
        console.log(`📁 Directory exists: ${dir}`);
    }
});

// Check for environment file
if (!fs.existsSync('.env')) {
    const envTemplate = `# Video CV Generator Environment Variables

# Google Cloud Service Account (required)
GOOGLE_APPLICATION_CREDENTIALS=./path/to/your/service-account.json

# Gemini API Key (required)
GEMINI_API_KEY=your_gemini_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

# File Storage
UPLOAD_DIR=uploads
OUTPUT_DIR=output

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
`;

    fs.writeFileSync('.env', envTemplate);
    console.log('✅ Created .env template file');
    console.log('⚠️  Please update the .env file with your actual API keys and credentials');
} else {
    console.log('📄 .env file already exists');
}

// Create gitignore if it doesn't exist
if (!fs.existsSync('.gitignore')) {
    const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
uploads/*
!uploads/.gitkeep
output/*
!output/.gitkeep

# Google Cloud credentials
*.json
service-account*.json

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Build outputs
dist/
build/
`;

    fs.writeFileSync('.gitignore', gitignoreContent);
    console.log('✅ Created .gitignore file');
} else {
    console.log('📄 .gitignore file already exists');
}

// Create placeholder files
const placeholders = [
    'uploads/.gitkeep',
    'output/.gitkeep'
];

placeholders.forEach(placeholder => {
    if (!fs.existsSync(placeholder)) {
        fs.writeFileSync(placeholder, '');
        console.log(`✅ Created placeholder: ${placeholder}`);
    }
});

console.log('\n🎉 Setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Update .env file with your Google Cloud credentials and Gemini API key');
console.log('2. Run "npm run check-services" to verify your API connections');
console.log('3. Run "npm run dev" to start the development server');
console.log('4. Open http://localhost:3000 in your browser');

```

Create `scripts/check-services.js`:

```jsx
const axios = require('axios');
const speech = require('@google-cloud/speech');
const vision = require('@google-cloud/vision');
require('dotenv').config();

async function checkServices() {
    console.log('🔍 Checking service connections...\n');

    const results = {
        googleCloud: false,
        gemini: false,
        ffmpeg: false
    };

    // Check Google Cloud Speech API
    try {
        const speechClient = new speech.SpeechClient();
        // This will throw an error if credentials are invalid
        await speechClient.initialize();
        console.log('✅ Google Cloud Speech API: Connected');
        results.googleCloud = true;
    } catch (error) {
        console.log('❌ Google Cloud Speech API: Failed');
        console.log(`   Error: ${error.message}`);
    }

    // Check Google Cloud Vision API
    try {
        const visionClient = new vision.ImageAnnotatorClient();
        await visionClient.initialize();
        console.log('✅ Google Cloud Vision API: Connected');
    } catch (error) {
        console.log('❌ Google Cloud Vision API: Failed');
        console.log(`   Error: ${error.message}`);
    }

    // Check Gemini API
    try {
        if (!process.env.GEMINI_API_KEY) {
            throw new Error('GEMINI_API_KEY not found in environment');
        }

        const response = await axios.post(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${process.env.GEMINI_API_KEY}`,
            {
                contents: [{
                    parts: [{
                        text: 'Hello, this is a test.'
                    }]
                }]
            },
            { timeout: 10000 }
        );

        if (response.status === 200) {
            console.log('✅ Gemini API: Connected');
            results.gemini = true;
        }
    } catch (error) {
        console.log('❌ Gemini API: Failed');
        console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
    }

    // Check FFmpeg
    try {
        const { exec } = require('child_process');
        const util = require('util');
        const execPromise = util.promisify(exec);

        await execPromise('ffmpeg -version');
        console.log('✅ FFmpeg: Installed and accessible');
        results.ffmpeg = true;
    } catch (error) {
        console.log('❌ FFmpeg: Not found');
        console.log('   Please install FFmpeg: https://ffmpeg.org/download.html');
    }

    console.log('\n📊 Service Status Summary:');
    console.log(`Google Cloud APIs: ${results.googleCloud ? '✅' : '❌'}`);
    console.log(`Gemini AI: ${results.gemini ? '✅' : '❌'}`);
    console.log(`FFmpeg: ${results.ffmpeg ? '✅' : '❌'}`);

    const allServicesWorking = Object.values(results).every(status => status);

    if (allServicesWorking) {
        console.log('\n🎉 All services are working! You can start using the application.');
    } else {
        console.log('\n⚠️  Some services need attention. Please fix the issues above before running the application.');
    }

    return allServicesWorking;
}

checkServices().catch(console.error);

```

Create `scripts/cleanup.js`:

```jsx
const fs = require('fs');
const path = require('path');

function cleanupDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
        console.log(`📁 Directory doesn't exist: ${dirPath}`);
        return;
    }

    const files = fs.readdirSync(dirPath);
    let deletedCount = 0;

    files.forEach(file => {
        if (file === '.gitkeep') return; // Keep placeholder files

        const filePath = path.join(dirPath, file);
        const stats = fs.lstatSync(filePath);

        if (stats.isDirectory()) {
            // Recursively delete subdirectories
            fs.rmSync(filePath, { recursive: true, force: true });
            deletedCount++;
        } else {
            fs.unlinkSync(filePath);
            deletedCount++;
        }
    });

    console.log(`🗑️  Deleted ${deletedCount} items from ${dirPath}`);
}

console.log('🧹 Cleaning up temporary files...\n');

// Clean upload and output directories
cleanupDirectory('uploads');
cleanupDirectory('output');

// Clean any frame directories that might be left behind
const uploadsPath = 'uploads';
if (fs.existsSync(uploadsPath)) {
    const items = fs.readdirSync(uploadsPath);
    items.forEach(item => {
        const itemPath = path.join(uploadsPath, item);
        if (fs.lstatSync(itemPath).isDirectory() && item === 'frames') {
            fs.rmSync(itemPath, { recursive: true, force: true });
            console.log('🗑️  Deleted frames directory');
        }
    });
}

console.log('\n✅ Cleanup complete!');

```

### Task 7.2: Documentation and README

**Subtask 7.2.1: Create Comprehensive README**
Create `README.md`:

```markdown
# Video CV Generator 🎥→📄

An AI-powered application that generates professional CVs from 30-second video introductions using computer vision, speech recognition, and natural language processing.

## 🌟 Features

- **Multimodal AI Analysis**: Combines audio transcription and visual analysis
- **Speech-to-Text**: Converts spoken content to structured text using Google Speech API
- **Computer Vision**: Analyzes presentation skills, eye contact, and professional appearance
- **AI-Powered Extraction**: Uses Gemini AI to extract CV information from video content
- **Professional Templates**: Generates modern, ATS-friendly CV layouts
- **PDF Export**: Creates downloadable PDF versions of generated CVs
- **Presentation Assessment**: Provides feedback on communication skills and professionalism

## 🏗️ Architecture

```

Video Upload → Audio/Visual Processing → AI Analysis → CV Generation → PDF Export
↓              ↓                       ↓              ↓
Validation    Speech-to-Text         Information      Template
Metadata      Computer Vision        Extraction       Generation

```

## 🚀 Quick Start

### Prerequisites

- Node.js 14.0.0 or higher
- FFmpeg installed on your system
- Google Cloud account with Speech and Vision APIs enabled
- Gemini API key

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd video-cv-generator
npm install

```

1. **Run setup script:**

```bash
npm run setup

```

1. **Configure environment variables:**

Update the `.env` file with your credentials:

```
# Google Cloud Service Account
GOOGLE_APPLICATION_CREDENTIALS=./path/to/service-account.json

# Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

```

1. **Verify service connections:**

```bash
npm run check-services

```

1. **Start the development server:**

```bash
npm run dev

```

1. **Open your browser:**
Navigate to `http://localhost:3000`

## 📋 API Endpoints

### POST `/api/process-video`

Processes uploaded video and generates CV.

**Request:**

- `video`: Video file (multipart/form-data)
- Supported formats: MP4, AVI, MOV, WEBM
- Max size: 100MB
- Recommended duration: ~30 seconds

**Response:**

```json
{
  "success": true,
  "cvData": { /* Extracted CV information */ },
  "cv_html": "<!-- Generated CV HTML -->",
  "pdf_url": "/download/cv_123456789.pdf",
  "analysis_summary": { /* Presentation analysis */ },
  "processing_stats": { /* Performance metrics */ }
}

```

### GET `/api/download/:filename`

Downloads generated PDF CV.

### GET `/api/health`

Service health check.

### GET `/api/supported-formats`

Returns supported video formats and requirements.

## 🎯 How It Works

### 1. Video Processing Pipeline

```jsx
Video Upload → Validation → Audio Extraction → Frame Extraction

```

### 2. Parallel AI Analysis

```jsx
Audio Stream → Speech-to-Text → Speech Pattern Analysis
Video Frames → Computer Vision → Presentation Assessment

```

### 3. Information Extraction

```jsx
Transcript + Visual Analysis → Gemini AI → Structured CV Data

```

### 4. CV Generation

```jsx
CV Data → HTML Template → PDF Generation → Download

```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
| --- | --- | --- |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to Google Cloud service account JSON | Yes |
| `GEMINI_API_KEY` | Gemini AI API key | Yes |
| `PORT` | Server port (default: 3000) | No |
| `UPLOAD_DIR` | Upload directory (default: uploads) | No |
| `OUTPUT_DIR` | Output directory (default: output) | No |
| `NODE_ENV` | Environment (development/production) | No |

### Google Cloud Setup

1. Create a Google Cloud project
2. Enable Speech-to-Text and Vision APIs
3. Create a service account
4. Download the service account JSON key
5. Set `GOOGLE_APPLICATION_CREDENTIALS` to the JSON file path

### Gemini API Setup

1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Create an API key
3. Set `GEMINI_API_KEY` in your environment

## 📊 Analysis Features

### Speech Analysis

- **Transcription accuracy**: Confidence scoring
- **Speech patterns**: Words per minute, pause analysis
- **Fluency assessment**: Professional speaking evaluation

### Visual Analysis

- **Face detection**: Professional appearance assessment
- **Eye contact**: Camera engagement measurement
- **Background analysis**: Professional setting evaluation
- **Text detection**: Visible credentials/certificates

### Content Extraction

- **Personal information**: Name, contact details
- **Professional summary**: Experience, role, objectives
- **Skills**: Technical and soft skills identification
- **Work experience**: Roles, companies, achievements
- **Education**: Degrees, institutions, certifications

## 🎨 CV Templates

The system generates modern, professional CV templates with:

- Clean, ATS-friendly layout
- Responsive design for all devices
- Professional color schemes
- Structured sections for easy reading
- Presentation assessment scores
- Print-optimized formatting

## 🛠️ Development

### Project Structure

```
video-cv-generator/
├── src/
│   ├── controllers/     # Request handlers
│   ├── services/        # AI service integrations
│   ├── middleware/      # Express middleware
│   ├── routes/         # API route

```