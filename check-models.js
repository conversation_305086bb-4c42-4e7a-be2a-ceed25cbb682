require('dotenv').config();
const https = require('https');

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
if (!GEMINI_API_KEY) {
  console.log('❌ GEMINI_API_KEY environment variable not found');
  console.log('Please check your .env file');
  process.exit(1);
}

console.log('🔑 Using API Key:', GEMINI_API_KEY.substring(0, 10) + '...');

const url = `https://generativelanguage.googleapis.com/v1beta/models?key=${GEMINI_API_KEY}`;

console.log('🌐 Making request to:', url.replace(GEMINI_API_KEY, 'API_KEY'));

https.get(url, (res) => {
  let data = '';

  console.log('📡 Response status:', res.statusCode);

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('\n📋 Available Gemini Models:');
      console.log('===========================');

      if (response.models && response.models.length > 0) {
        response.models.forEach((model, index) => {
          console.log(`${index + 1}. ${model.name}`);
          console.log(`   Display Name: ${model.displayName}`);
          console.log(`   Description: ${model.description}`);
          console.log(`   Supported Methods: ${model.supportedGenerationMethods?.join(', ') || 'N/A'}`);
          console.log('');
        });
      } else {
        console.log('No models found in response');
        console.log('Response keys:', Object.keys(response));
      }
    } catch (error) {
      console.log('❌ Error parsing response:', error.message);
      console.log('Raw response:', data.substring(0, 500) + '...');
    }
  });
}).on('error', (error) => {
  console.log('❌ Request failed:', error.message);
});