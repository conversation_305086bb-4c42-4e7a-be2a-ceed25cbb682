class CVTemplate {

    generateModernCV(cvData) {
        const personalInfo = cvData.personalInfo || {};
        const professionalSummary = cvData.professionalSummary || {};
        const skills = cvData.skills || {};
        const workExperience = cvData.workExperience || [];
        const education = cvData.education || [];
        const additionalInfo = cvData.additionalInfo || {};
        const presentationAssessment = cvData.presentationAssessment || {};

        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${personalInfo.name || 'Professional Resume'}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .cv-container {
            max-width: 850px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-radius: 16px;
            overflow: hidden;
            position: relative;
        }

        .cv-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
        }

        .cv-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cv-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        .cv-header h1 {
            font-size: 3.2em;
            font-weight: 700;
            margin-bottom: 12px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        .cv-header p {
            font-size: 1.4em;
            opacity: 0.95;
            font-weight: 300;
            position: relative;
            z-index: 2;
        }

        .contact-info {
            margin-top: 30px;
            font-size: 1em;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .contact-info span {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .contact-info span:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .section {
            margin: 0;
            padding: 40px;
            border-left: none;
            background: #ffffff;
            position: relative;
        }

        .section:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .section h2 {
            color: #667eea;
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 25px;
            position: relative;
            padding-bottom: 15px;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section p {
            margin-bottom: 15px;
            color: #4a5568;
            font-size: 1.05em;
            line-height: 1.7;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .skill-category {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .skill-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .skill-category h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .skill-tag {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            margin: 4px 6px 4px 0;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .skill-tag:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .experience-item, .education-item {
            margin-bottom: 25px;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .experience-item::before, .education-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #667eea, #764ba2);
        }

        .experience-item:hover, .education-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        }

        .experience-item h3, .education-item h3 {
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .experience-item .company, .education-item .institution {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .experience-item ul, .education-item ul {
            margin-top: 10px;
            padding-left: 20px;
        }

        .experience-item li, .education-item li {
            margin-bottom: 5px;
            color: #4a5568;
        }

        .presentation-assessment {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            margin: 0;
            position: relative;
            overflow: hidden;
        }

        .presentation-assessment::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .presentation-assessment h2 {
            color: white;
            font-size: 2em;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }

        .assessment-item {
            text-align: center;
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .assessment-item:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-3px);
        }

        .assessment-item .score {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .assessment-item .label {
            font-size: 1em;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .cv-container {
                margin: 10px;
                border-radius: 8px;
            }

            .cv-header {
                padding: 40px 20px;
            }

            .cv-header h1 {
                font-size: 2.5em;
            }

            .cv-header p {
                font-size: 1.2em;
            }

            .contact-info {
                flex-direction: column;
                gap: 10px;
            }

            .section {
                padding: 25px 20px;
            }

            .section h2 {
                font-size: 1.5em;
            }

            .skills-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .assessment-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .presentation-assessment {
                padding: 25px 20px;
            }

            .presentation-assessment h2 {
                font-size: 1.6em;
            }
        }

        @media (max-width: 480px) {
            .cv-header h1 {
                font-size: 2em;
            }

            .assessment-grid {
                grid-template-columns: 1fr;
            }

            .skill-tag {
                font-size: 0.8em;
                padding: 6px 12px;
            }
        }

        @media print {
            body {
                background: white !important;
            }

            .cv-container {
                box-shadow: none;
                border-radius: 0;
            }

            .cv-header::before,
            .presentation-assessment::before {
                display: none;
            }

            .skill-category:hover,
            .experience-item:hover,
            .education-item:hover,
            .assessment-item:hover,
            .contact-info span:hover,
            .skill-tag:hover {
                transform: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header Section -->
        <div class="cv-header">
            <h1>${personalInfo.name || 'Professional'}</h1>
            <p>${professionalSummary.currentRole || 'Professional'}</p>
            <div class="contact-info">
                ${personalInfo.email ? `<span>📧 ${personalInfo.email}</span>` : ''}
                ${personalInfo.phone ? `<span>📱 ${personalInfo.phone}</span>` : ''}
                ${personalInfo.location ? `<span>📍 ${personalInfo.location}</span>` : ''}
            </div>
        </div>

        ${this.generatePresentationAssessment(presentationAssessment)}
        ${this.generateProfessionalSummary(professionalSummary)}
        ${this.generateSkillsSection(skills)}
        ${this.generateExperienceSection(workExperience)}
        ${this.generateEducationSection(education)}
        ${this.generateAdditionalSection(additionalInfo)}
    </div>
</body>
</html>`;
    }

    generatePresentationAssessment(assessment) {
        if (!assessment || Object.keys(assessment).length === 0) return '';

        return `
        <div class="presentation-assessment">
            <h2>🎭 Presentation Assessment</h2>
            <p style="margin-bottom: 25px; font-size: 1.1em; opacity: 0.95;">Based on your video analysis</p>
            <div class="assessment-grid">
                <div class="assessment-item">
                    <div class="score">${assessment.communicationClarity || 'N/A'}</div>
                    <div class="label">Communication Clarity</div>
                </div>
                <div class="assessment-item">
                    <div class="score">${assessment.professionalDemeanor || 'N/A'}</div>
                    <div class="label">Professional Demeanor</div>
                </div>
                <div class="assessment-item">
                    <div class="score">${assessment.confidenceLevel || 'N/A'}</div>
                    <div class="label">Confidence Level</div>
                </div>
                <div class="assessment-item">
                    <div class="score">${assessment.overallQuality || 'N/A'}</div>
                    <div class="label">Overall Quality</div>
                </div>
            </div>
            ${assessment.improvements && assessment.improvements.length > 0 ?
                `<div style="margin-top: 25px; padding: 20px; background: rgba(255,255,255,0.15); border-radius: 8px; backdrop-filter: blur(10px);">
                    <strong style="font-size: 1.1em;">💡 Areas for Improvement:</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        ${assessment.improvements.map(item => `<li style="margin-bottom: 5px;">${item}</li>`).join('')}
                    </ul>
                </div>` : ''}
        </div>`;
    }

    generateProfessionalSummary(summary) {
        if (!summary || Object.keys(summary).length === 0) return '';

        return `
        <div class="section">
            <h2>👨‍💼 Professional Summary</h2>
            ${summary.currentRole ? `<p><strong>Current Role:</strong> ${summary.currentRole}</p>` : ''}
            ${summary.yearsExperience ? `<p><strong>Experience:</strong> ${summary.yearsExperience} years</p>` : ''}
            ${summary.keyStrengths && summary.keyStrengths.length > 0 ? 
                `<p><strong>Key Strengths:</strong> ${summary.keyStrengths.join(', ')}</p>` : ''}
            ${summary.careerObjective ? `<p><strong>Career Objective:</strong> ${summary.careerObjective}</p>` : ''}
        </div>`;
    }

    generateSkillsSection(skills) {
        if (!skills || ((!skills.technical || skills.technical.length === 0) &&
                      (!skills.soft || skills.soft.length === 0) &&
                      (!skills.tools || skills.tools.length === 0))) return '';

        return `
        <div class="section">
            <h2>🛠️ Skills & Expertise</h2>
            <div class="skills-grid">
                ${skills.technical && skills.technical.length > 0 ? `
                    <div class="skill-category">
                        <h3>Technical Skills</h3>
                        ${skills.technical.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                    </div>
                ` : ''}
                ${skills.soft && skills.soft.length > 0 ? `
                    <div class="skill-category">
                        <h3>Soft Skills</h3>
                        ${skills.soft.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                    </div>
                ` : ''}
                ${skills.tools && skills.tools.length > 0 ? `
                    <div class="skill-category">
                        <h3>Tools & Technologies</h3>
                        ${skills.tools.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        </div>`;
    }

    generateExperienceSection(workExperience) {
        if (!workExperience || workExperience.length === 0) return '';

        return `
        <div class="section">
            <h2>💼 Work Experience</h2>
            ${workExperience.map(exp => `
                <div class="experience-item">
                    <h3>${exp.position || 'Position'}</h3>
                    <div class="company">${exp.company || 'Company'}</div>
                    ${exp.duration ? `<p><strong>Duration:</strong> ${exp.duration}</p>` : ''}
                    ${exp.achievements && exp.achievements.length > 0 ? 
                        `<p><strong>Achievements:</strong></p><ul>${exp.achievements.map(ach => `<li>${ach}</li>`).join('')}</ul>` : ''}
                </div>
            `).join('')}
        </div>`;
    }

    generateEducationSection(education) {
        if (!education || education.length === 0) return '';

        return `
        <div class="section">
            <h2>🎓 Education</h2>
            ${education.map(edu => `
                <div class="education-item">
                    <h3>${edu.degree || 'Degree'}</h3>
                    <div class="institution">${edu.institution || 'Institution'}</div>
                    ${edu.year ? `<p><strong>Year:</strong> ${edu.year}</p>` : ''}
                </div>
            `).join('')}
        </div>`;
    }

    generateAdditionalSection(additionalInfo) {
        if (!additionalInfo || Object.keys(additionalInfo).length === 0) return '';

        return `
        <div class="section">
            <h2>🌟 Additional Information</h2>
            ${additionalInfo.certifications && additionalInfo.certifications.length > 0 ? 
                `<p><strong>Certifications:</strong> ${additionalInfo.certifications.join(', ')}</p>` : ''}
            ${additionalInfo.languages && additionalInfo.languages.length > 0 ? 
                `<p><strong>Languages:</strong> ${additionalInfo.languages.join(', ')}</p>` : ''}
            ${additionalInfo.interests && additionalInfo.interests.length > 0 ? 
                `<p><strong>Interests:</strong> ${additionalInfo.interests.join(', ')}</p>` : ''}
        </div>`;
    }
}

module.exports = new CVTemplate();