const express = require('express');
const upload = require('../middleware/upload');
const VideoController = require('../controllers/videoController');

const router = express.Router();
const videoController = new VideoController();

console.log('🔧 API routes module loaded');

// Middleware to track processing time
router.use((req, res, next) => {
    req.startTime = Date.now();
    next();
});

// Video processing endpoint
router.post('/process-video', upload.single('video'), (req, res, next) => {
    console.log('📡 /process-video route hit');
    console.log('📁 Request file:', req.file);
    videoController.processVideo.bind(videoController)(req, res, next);
});

// Progress tracking endpoint
router.get('/progress/:sessionId', (req, res) => {
    const { sessionId } = req.params;
    const progress = videoController.getProgress(sessionId);
    res.json(progress);
});

// Download generated CV
router.get('/download/:filename', videoController.downloadCV.bind(videoController));

// Health check endpoint
router.get('/health', (req, res) => {
    console.log('🏥 Health check requested');
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        services: {
            googleCloud: process.env.GOOGLE_APPLICATION_CREDENTIALS ? 'configured' : 'not configured',
            gemini: process.env.GEMINI_API_KEY ? 'configured' : 'not configured'
        }
    });
});

// Get supported video formats
router.get('/supported-formats', (req, res) => {
    res.json({
        formats: ['mp4', 'avi', 'mov', 'webm'],
        maxSize: '100MB',
        recommendedDuration: '30 seconds',
        requirements: [
            'Must contain both audio and video streams',
            'Duration between 15-45 seconds',
            'File size under 100MB'
        ]
    });
});

module.exports = router;