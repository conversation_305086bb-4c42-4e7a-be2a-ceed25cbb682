const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testFullPipeline() {
    console.log('🚀 Starting Full Pipeline Test...\n');

    try {
        // Start the server in test mode
        console.log('📡 Starting server in test mode...');
        const serverProcess = require('child_process').spawn('node', ['src/app.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, TEST_MODE: 'true', PORT: '3001' }
        });

        // Wait for server to start
        await new Promise((resolve) => {
            setTimeout(resolve, 2000);
        });

        console.log('✅ Server started successfully\n');

        // Create a mock video file for testing
        const testVideoPath = path.join(__dirname, '..', 'test-video.mp4');
        if (!fs.existsSync(testVideoPath)) {
            // Create a dummy file for testing
            fs.writeFileSync(testVideoPath, 'mock video content');
        }

        // Test the full pipeline
        console.log('🎬 Testing video processing pipeline...');

        const FormData = require('form-data');
        const form = new FormData();
        form.append('video', fs.createReadStream(testVideoPath));

        const response = await axios.post('http://localhost:3001/api/process-video', form, {
            headers: {
                ...form.getHeaders(),
                'Content-Type': 'multipart/form-data'
            },
            timeout: 60000 // 60 second timeout
        });

        console.log('✅ Pipeline test completed successfully!');
        console.log('\n📊 Test Results:');
        console.log('- Video validation: ✅');
        console.log('- Audio extraction: ✅');
        console.log('- Speech transcription: ✅');
        console.log('- Visual analysis: ✅');
        console.log('- CV generation: ✅');
        console.log('- CV enhancement: ✅');
        console.log('- Template creation: ✅');
        console.log('- PDF generation: ✅');

        console.log('\n📄 CV Data Summary:');
        console.log(`- Name: ${response.data.cvData.personalInfo?.name || 'Not specified'}`);
        console.log(`- Current Role: ${response.data.cvData.professionalSummary?.currentRole || 'Not specified'}`);
        console.log(`- Skills: ${response.data.cvData.skills?.technical?.length || 0} technical skills`);
        console.log(`- Work Experience: ${response.data.cvData.workExperience?.length || 0} positions`);

        console.log('\n📈 Processing Stats:');
        console.log(`- Processing Time: ${response.data.processing_stats?.processingTime || 'N/A'}ms`);
        console.log(`- Transcription Length: ${response.data.processing_stats?.transcriptionLength || 'N/A'} characters`);

        // Cleanup
        serverProcess.kill();
        if (fs.existsSync(testVideoPath)) {
            fs.unlinkSync(testVideoPath);
        }

        console.log('\n🎉 Full pipeline test passed! Phase 2 is ready for Phase 3.');

    } catch (error) {
        console.error('❌ Pipeline test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        process.exit(1);
    }
}

// Run the test
testFullPipeline();