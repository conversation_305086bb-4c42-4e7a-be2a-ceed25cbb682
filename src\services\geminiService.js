const axios = require('axios');

class GeminiService {
    constructor() {
        this.apiKey = process.env.GEMINI_API_KEY;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        this.model = 'gemini-pro-latest';
    }

    async extractCVInformation(transcript, visualAnalysis) {
        const prompt = this.buildExtractionPrompt(transcript, visualAnalysis);

        console.log('🤖 Calling Gemini API for CV extraction...');
        try {
            const response = await axios.post(
                `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`,
                {
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "object",
                            properties: {
                                personalInfo: {
                                    type: "object",
                                    properties: {
                                        name: { type: "string" },
                                        email: { type: "string" },
                                        phone: { type: "string" },
                                        location: { type: "string" }
                                    }
                                },
                                professionalSummary: {
                                    type: "object",
                                    properties: {
                                        currentRole: { type: "string" },
                                        yearsExperience: { type: "number" },
                                        keyStrengths: { type: "array", items: { type: "string" } },
                                        careerObjective: { type: "string" }
                                    }
                                },
                                skills: {
                                    type: "object",
                                    properties: {
                                        technical: { type: "array", items: { type: "string" } },
                                        soft: { type: "array", items: { type: "string" } },
                                        tools: { type: "array", items: { type: "string" } }
                                    }
                                },
                                workExperience: {
                                    type: "array",
                                    items: {
                                        type: "object",
                                        properties: {
                                            position: { type: "string" },
                                            company: { type: "string" },
                                            duration: { type: "string" },
                                            achievements: { type: "array", items: { type: "string" } }
                                        }
                                    }
                                },
                                education: {
                                    type: "array",
                                    items: {
                                        type: "object",
                                        properties: {
                                            degree: { type: "string" },
                                            institution: { type: "string" },
                                            year: { type: "string" }
                                        }
                                    }
                                },
                                presentationAssessment: {
                                    type: "object",
                                    properties: {
                                        communicationClarity: { type: "number" },
                                        professionalDemeanor: { type: "number" },
                                        confidenceLevel: { type: "number" },
                                        overallQuality: { type: "number" },
                                        improvements: { type: "array", items: { type: "string" } }
                                    }
                                },
                                additionalInfo: {
                                    type: "object",
                                    properties: {
                                        certifications: { type: "array", items: { type: "string" } },
                                        languages: { type: "array", items: { type: "string" } },
                                        interests: { type: "array", items: { type: "string" } }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 30000 // 30 second timeout
                }
            );

            const generatedText = response.data.candidates[0].content.parts[0].text;
            console.log('✅ Gemini API call completed successfully');
            return this.parseStructuredResponse(generatedText);

        } catch (error) {
            console.error('❌ Gemini API call failed:', error.message);
            console.error('CV extraction failed:', error.response?.data || error.message);
            throw new Error(`CV extraction failed: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    buildExtractionPrompt(transcript, visualAnalysis) {
        return `
You are an expert HR professional analyzing a candidate's video introduction for CV generation.

TRANSCRIPT OF SPOKEN CONTENT:
"${transcript}"

VISUAL ANALYSIS DATA:
- Overall Professional Score: ${visualAnalysis.overallProfessionalScore}/100
- Eye Contact Quality: ${visualAnalysis.summary.presentationQuality.eyeContact.assessment}
- Background Setting: ${visualAnalysis.summary.presentationQuality.background.assessment}
- Enthusiasm Level: ${visualAnalysis.summary.presentationQuality.enthusiasm.assessment}
- Professional Keywords Detected: ${visualAnalysis.textAnalysis.professionalKeywords.join(', ') || 'None'}
- Credentials Visible: ${visualAnalysis.textAnalysis.hasCredentials ? 'Yes' : 'No'}

TASK: Extract structured CV information from the transcript and visual cues. Pay attention to:

1. PERSONAL INFORMATION:
   - Name (if mentioned)
   - Contact information (email, phone, LinkedIn if mentioned)
   - Location (if mentioned)

2. PROFESSIONAL SUMMARY:
   - Current role/title
   - Years of experience
   - Key professional strengths
   - Career objectives

3. SKILLS AND EXPERTISE:
   - Technical skills mentioned
   - Soft skills demonstrated or mentioned
   - Programming languages, tools, technologies
   - Industry-specific competencies

4. WORK EXPERIENCE:
   - Current position
   - Previous roles (if mentioned)
   - Companies worked for
   - Key achievements or projects
   - Responsibilities mentioned

5. EDUCATION:
   - Degrees mentioned
   - Institutions
   - Certifications or courses
   - Relevant academic achievements

6. PRESENTATION SKILLS ASSESSMENT:
   - Communication clarity
   - Professional demeanor
   - Confidence level
   - Overall presentation quality

7. ADDITIONAL QUALIFICATIONS:
   - Languages spoken
   - Volunteer work or leadership roles
   - Awards or recognitions
   - Professional memberships
   - Publications or projects

IMPORTANT INSTRUCTIONS:
- Extract only information explicitly mentioned or clearly implied in the transcript
- Do not invent or assume information not provided
- Use the visual analysis to enhance professional assessment
- If information is not available, mark as "Not specified"
- Be conservative in your extractions - accuracy over completeness

RESPOND ONLY WITH VALID JSON in this exact format:
{
  "personalInfo": {
    "name": "string or null",
    "email": "string or null",
    "phone": "string or null",
    "location": "string or null"
  },
  "professionalSummary": {
    "currentRole": "string or null",
    "yearsExperience": "number or null",
    "keyStrengths": ["array of strings"],
    "careerObjective": "string or null"
  },
  "skills": {
    "technical": ["array of strings"],
    "soft": ["array of strings"],
    "tools": ["array of strings"]
  },
  "workExperience": [
    {
      "position": "string",
      "company": "string",
      "duration": "string or null",
      "achievements": ["array of strings"]
    }
  ],
  "education": [
    {
      "degree": "string",
      "institution": "string",
      "year": "string or null"
    }
  ],
  "presentationAssessment": {
    "communicationClarity": "number (1-10)",
    "professionalDemeanor": "number (1-10)",
    "confidenceLevel": "number (1-10)",
    "overallQuality": "number (1-10)",
    "improvements": ["array of strings"]
  },
  "additionalInfo": {
    "certifications": ["array of strings"],
    "languages": ["array of strings"],
    "interests": ["array of strings"]
  }
}

DO NOT include any text outside of the JSON structure. The response must be parseable JSON only.
`;
    }

    parseStructuredResponse(generatedText) {
        try {
            // Since we're using responseMimeType: "application/json", the response should be valid JSON
            return JSON.parse(generatedText);
        } catch (error) {
            console.error('Failed to parse structured response:', error);
            console.error('Raw response:', generatedText);
            return {};
        }
    }

    async enhanceCV(cvData, additionalContext = {}) {
        const enhancementPrompt = `
Given this extracted CV data:
${JSON.stringify(cvData, null, 2)}

Additional Context:
- Video duration: ${additionalContext.videoDuration || 'Unknown'}
- Speech fluency score: ${additionalContext.speechFluency || 'Unknown'}
- Professional setting score: ${additionalContext.backgroundScore || 'Unknown'}

TASK: Enhance and refine the CV data by:
1. Improving descriptions and summaries
2. Adding professional formatting suggestions
3. Identifying potential gaps or areas for improvement
4. Providing industry-specific recommendations

Respond with enhanced JSON in the same structure.
        `;

        try {
            console.log('🤖 Calling Gemini API for CV enhancement...');
            const response = await axios.post(
                `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`,
                {
                    contents: [{
                        parts: [{
                            text: enhancementPrompt
                        }]
                    }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "object",
                            properties: {
                                personalInfo: {
                                    type: "object",
                                    properties: {
                                        name: { type: "string" },
                                        email: { type: "string" },
                                        phone: { type: "string" },
                                        location: { type: "string" }
                                    }
                                },
                                professionalSummary: {
                                    type: "object",
                                    properties: {
                                        currentRole: { type: "string" },
                                        yearsExperience: { type: "number" },
                                        keyStrengths: { type: "array", items: { type: "string" } },
                                        careerObjective: { type: "string" }
                                    }
                                },
                                skills: {
                                    type: "object",
                                    properties: {
                                        technical: { type: "array", items: { type: "string" } },
                                        soft: { type: "array", items: { type: "string" } },
                                        tools: { type: "array", items: { type: "string" } }
                                    }
                                },
                                workExperience: {
                                    type: "array",
                                    items: {
                                        type: "object",
                                        properties: {
                                            position: { type: "string" },
                                            company: { type: "string" },
                                            duration: { type: "string" },
                                            achievements: { type: "array", items: { type: "string" } }
                                        }
                                    }
                                },
                                education: {
                                    type: "array",
                                    items: {
                                        type: "object",
                                        properties: {
                                            degree: { type: "string" },
                                            institution: { type: "string" },
                                            year: { type: "string" }
                                        }
                                    }
                                },
                                presentationAssessment: {
                                    type: "object",
                                    properties: {
                                        communicationClarity: { type: "number" },
                                        professionalDemeanor: { type: "number" },
                                        confidenceLevel: { type: "number" },
                                        overallQuality: { type: "number" },
                                        improvements: { type: "array", items: { type: "string" } }
                                    }
                                },
                                additionalInfo: {
                                    type: "object",
                                    properties: {
                                        certifications: { type: "array", items: { type: "string" } },
                                        languages: { type: "array", items: { type: "string" } },
                                        interests: { type: "array", items: { type: "string" } }
                                    }
                                },
                                recommendations: {
                                    type: "array",
                                    items: { type: "string" }
                                }
                            }
                        }
                    }
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 45000 // 45 second timeout for enhancement
                }
            );

            const generatedText = response.data.candidates[0].content.parts[0].text;
            console.log('✅ Gemini enhancement completed successfully');
            return this.parseStructuredResponse(generatedText);

        } catch (error) {
            console.error('❌ Gemini enhancement failed:', error.message);
            console.error('CV enhancement failed:', error.response?.data || error.message);
            return cvData;
        }
    }

    getMockCVData() {
        return {
            personalInfo: {
                name: "Alex Johnson",
                email: "<EMAIL>",
                phone: "+****************",
                location: "San Francisco, CA",
                linkedin: "linkedin.com/in/alexjohnson"
            },
            professionalSummary: {
                currentRole: "Senior Software Developer",
                yearsExperience: 5,
                keySkills: ["Full-Stack Development", "Node.js", "React", "Cloud Technologies"],
                careerObjective: "Seeking challenging opportunities in software development where I can leverage my expertise in modern web technologies and AI-powered applications."
            },
            skills: {
                technical: ["JavaScript", "Node.js", "React", "Python", "MongoDB", "PostgreSQL", "AWS", "Docker"],
                soft: ["Problem Solving", "Team Collaboration", "Project Management", "Communication"]
            },
            workExperience: [
                {
                    position: "Senior Software Developer",
                    company: "Tech Innovations Inc.",
                    duration: "2020 - Present",
                    responsibilities: [
                        "Led development of e-commerce platform serving 100K+ users",
                        "Implemented AI-powered recommendation system",
                        "Mentored junior developers and conducted code reviews"
                    ]
                },
                {
                    position: "Software Developer",
                    company: "StartupXYZ",
                    duration: "2018 - 2020",
                    responsibilities: [
                        "Developed full-stack web applications using MERN stack",
                        "Integrated third-party APIs and payment systems",
                        "Participated in agile development processes"
                    ]
                }
            ],
            education: [
                {
                    degree: "Bachelor of Science in Computer Science",
                    institution: "Massachusetts Institute of Technology",
                    year: "2018",
                    gpa: "3.8/4.0"
                }
            ],
            additionalInfo: {
                languages: ["English (Native)", "Spanish (Conversational)"],
                certifications: ["AWS Certified Developer", "Google Cloud Professional"],
                projects: ["AI-powered CV generator", "E-commerce platform", "Real-time chat application"]
            },
            presentationAssessment: {
                communicationClarity: 8,
                professionalDemeanor: 9,
                confidenceLevel: 8,
                overallQuality: 8
            }
        };
    }

    getMockEnhancedCV(cvData) {
        // Return the original CV data with a recommendations section added
        return {
            ...cvData,
            recommendations: [
                "Consider adding more specific metrics to your work experience descriptions",
                "Include quantifiable achievements to strengthen your professional summary",
                "Add more details about your leadership experience and team management",
                "Consider highlighting your international experience or cross-cultural communication skills"
            ]
        };
    }
}

module.exports = new GeminiService();