const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import routes
const apiRoutes = require('./routes/api');

class Application {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
        this.ensureDirectories();
    }

    setupMiddleware() {
        // Enable CORS for frontend integration
        this.app.use(cors({
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true
        }));

        // Parse JSON bodies
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // Serve static files
        this.app.use(express.static(path.join(__dirname, '../public')));
    }

    setupRoutes() {
        console.log('🔧 Setting up routes...');
        // API routes
        this.app.use('/api', apiRoutes);
        console.log('✅ API routes loaded');

        // Serve index.html for root path
        this.app.get('/', (req, res) => {
            console.log('📄 Serving index.html');
            res.sendFile(path.join(__dirname, '../public/index.html'));
        });
    }

    setupErrorHandling() {
        // Handle multer errors (file upload issues)
        this.app.use((error, req, res, next) => {
            if (error.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({ error: 'File too large. Maximum size is 100MB.' });
            }
            if (error.code === 'LIMIT_UNEXPECTED_FILE') {
                return res.status(400).json({ error: 'Unexpected file field.' });
            }
            next(error);
        });

        // Global error handler
        this.app.use((error, req, res, next) => {
            console.error('Unhandled error:', error);
            res.status(500).json({ 
                error: 'Internal server error',
                message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
            });
        });

        // 404 handler
        this.app.use((req, res) => {
            res.status(404).json({ error: 'Route not found' });
        });
    }

    ensureDirectories() {
        // Create necessary directories if they don't exist
        const dirs = [
            process.env.UPLOAD_DIR || 'uploads',
            process.env.OUTPUT_DIR || 'output'
        ];

        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`✅ Created directory: ${dir}`);
            }
        });
    }

    start() {
        try {
            const server = this.app.listen(this.port, '127.0.0.1', () => {
                console.log(`🚀 Video CV Generator server running on port ${this.port}`);
                console.log(`📁 Uploads directory: ${process.env.UPLOAD_DIR || 'uploads'}`);
                console.log(`📤 Output directory: ${process.env.OUTPUT_DIR || 'output'}`);
                console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
                console.log(`🔗 API endpoints available at http://localhost:${this.port}/api`);
                console.log(`🔍 Server listening on http://localhost:${this.port}`);
            });

            server.on('error', (error) => {
                console.error('❌ Server error:', error);
                process.exit(1);
            });

            // Graceful shutdown - temporarily disabled for debugging
            // process.on('SIGINT', this.shutdown.bind(this));
            // process.on('SIGTERM', this.shutdown.bind(this));
        } catch (error) {
            console.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }

    shutdown() {
        console.log('\n🛑 Shutting down server...');
        this.cleanupTempFiles();
        console.log('✅ Cleanup completed');
        process.exit(0);
    }

    cleanupTempFiles() {
        try {
            const uploadDir = process.env.UPLOAD_DIR || 'uploads';
            const outputDir = process.env.OUTPUT_DIR || 'output';

            // Clean old files (older than 1 hour)
            const maxAge = 60 * 60 * 1000; // 1 hour
            const now = Date.now();

            [uploadDir, outputDir].forEach(dir => {
                if (fs.existsSync(dir)) {
                    const files = fs.readdirSync(dir);
                    files.forEach(file => {
                        const filePath = path.join(dir, file);
                        const stats = fs.statSync(filePath);
                        if (now - stats.mtime.getTime() > maxAge) {
                            fs.unlinkSync(filePath);
                            console.log(`🗑️ Cleaned up old file: ${file}`);
                        }
                    });
                }
            });
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
}

module.exports = Application;

// Start the server if this file is run directly
if (require.main === module) {
    const app = new Application();
    app.start();
}