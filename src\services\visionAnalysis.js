const vision = require('@google-cloud/vision');
const fs = require('fs');

class VisionAnalysisService {
    constructor() {
        this.client = new vision.ImageAnnotatorClient();
    }

    async analyzeFrames(framePaths) {
        const analysisResults = [];

        // Process each frame to build comprehensive visual profile
        for (const framePath of framePaths) {
            try {
                const result = await this.analyzeSingleFrame(framePath);
                analysisResults.push({ framePath, ...result });
            } catch (error) {
                console.error(`Error analyzing frame ${framePath}:`, error);
            }
        }

        // Aggregate results across all frames
        return this.aggregateFrameAnalysis(analysisResults);
    }

    async analyzeSingleFrame(framePath) {
        const image = fs.readFileSync(framePath);

        // Perform multiple types of analysis simultaneously
        const [
            faceResults,
            objectResults,
            textResults,
            labelResults
        ] = await Promise.all([
            this.client.faceDetection({ image }),
            this.client.objectLocalization({ image }),
            this.client.textDetection({ image }),
            this.client.labelDetection({ image })
        ]);

        return {
            faces: this.processFaceData(faceResults[0]),
            objects: this.processObjectData(objectResults[0]),
            text: this.processTextData(textResults[0]),
            labels: this.processLabelData(labelResults[0]),
            timestamp: Date.now()
        };
    }

    processFaceData(faceResult) {
        if (!faceResult.faceAnnotations || faceResult.faceAnnotations.length === 0) {
            return { detected: false };
        }

        const face = faceResult.faceAnnotations[0]; // Assume single person

        // Analyze facial expressions and presentation quality
        return {
            detected: true,
            joyLikelihood: face.joyLikelihood,
            sorrowLikelihood: face.sorrowLikelihood,
            angerLikelihood: face.angerLikelihood,
            surpriseLikelihood: face.surpriseLikelihood,
            underExposedLikelihood: face.underExposedLikelihood,
            blurredLikelihood: face.blurredLikelihood,
            headwearLikelihood: face.headwearLikelihood,
            eyeContact: this.assessEyeContact(face),
            facePosition: this.assessFacePosition(face.boundingPoly),
            professionalScore: this.calculateProfessionalScore(face)
        };
    }

    processTextData(textResult) {
        if (!textResult.textAnnotations || textResult.textAnnotations.length === 0) {
            return { detectedText: [] };
        }

        // Extract any visible text (certificates, company names, etc.)
        const detectedTexts = textResult.textAnnotations.map(annotation => ({
            text: annotation.description,
            confidence: annotation.confidence || 0,
            position: annotation.boundingPoly
        }));

        // Look for professional keywords in detected text
        const professionalKeywords = this.extractProfessionalKeywords(detectedTexts);

        return {
            detectedText: detectedTexts,
            professionalKeywords: professionalKeywords,
            hasCredentials: professionalKeywords.length > 0
        };
    }

    processObjectData(objectResult) {
        if (!objectResult.localizedObjectAnnotations) {
            return { objects: [] };
        }

        // Analyze objects for professional context
        const objects = objectResult.localizedObjectAnnotations.map(obj => ({
            name: obj.name,
            confidence: obj.score,
            position: obj.boundingPoly
        }));

        // Categorize objects by professional relevance
        const professionalObjects = objects.filter(obj =>
            this.isProfessionalObject(obj.name)
        );

        return {
            objects: objects,
            professionalObjects: professionalObjects,
            backgroundScore: this.assessBackground(objects)
        };
    }

    processLabelData(labelResult) {
        if (!labelResult.labelAnnotations) {
            return { labels: [] };
        }

        const labels = labelResult.labelAnnotations.map(label => ({
            description: label.description,
            confidence: label.confidence || label.topicality || 0,
            score: label.score || 0
        }));

        return {
            labels: labels,
            professionalContext: this.assessProfessionalContext(labels)
        };
    }

    // Helper methods for analysis
    getLikelihoodScore(likelihood) {
        const scores = {
            'VERY_UNLIKELY': 0,
            'UNLIKELY': 25,
            'POSSIBLE': 50,
            'LIKELY': 75,
            'VERY_LIKELY': 100
        };
        return scores[likelihood] || 0;
    }

    assessEyeContact(face) {
        // Analyze if person is looking at camera
        const panAngle = Math.abs(face.panAngle || 0);
        const tiltAngle = Math.abs(face.tiltAngle || 0);

        // Good eye contact means minimal head movement
        if (panAngle < 15 && tiltAngle < 15) {
            return { score: 85, assessment: 'Good eye contact' };
        } else if (panAngle < 30 && tiltAngle < 30) {
            return { score: 60, assessment: 'Moderate eye contact' };
        } else {
            return { score: 30, assessment: 'Poor eye contact' };
        }
    }

    assessFacePosition(boundingPoly) {
        // Analyze if face is well-centered and appropriately sized
        // This is a simplified assessment - in production you'd want more sophisticated analysis
        return {
            centered: true, // Placeholder - implement actual centering logic
            appropriatelySized: true, // Placeholder
            score: 75
        };
    }

    calculateProfessionalScore(face) {
        let score = 50; // Base score

        // Positive indicators
        if (this.getLikelihoodScore(face.joyLikelihood) > 50) score += 20;
        if (this.getLikelihoodScore(face.angerLikelihood) < 25) score += 15;

        // Negative indicators
        if (this.getLikelihoodScore(face.angerLikelihood) > 50) score -= 30;

        return Math.max(0, Math.min(100, score));
    }

    extractProfessionalKeywords(detectedTexts) {
        const keywords = ['Certificate', 'Degree', 'Award', 'Company', 'LinkedIn', 'Resume'];
        const found = [];

        detectedTexts.forEach(textItem => {
            keywords.forEach(keyword => {
                if (textItem.text.toLowerCase().includes(keyword.toLowerCase())) {
                    found.push(keyword);
                }
            });
        });

        return [...new Set(found)]; // Remove duplicates
    }

    isProfessionalObject(objectName) {
        const professionalObjects = [
            'Computer', 'Laptop', 'Book', 'Certificate', 'Office',
            'Suit', 'Tie', 'Whiteboard', 'Presentation'
        ];
        return professionalObjects.some(prof =>
            objectName.toLowerCase().includes(prof.toLowerCase())
        );
    }

    assessBackground(objects) {
        // Analyze background for professionalism
        const professionalObjects = objects.filter(obj => this.isProfessionalObject(obj.name));

        if (professionalObjects.length >= 2) {
            return { score: 85, assessment: 'Professional background' };
        } else if (professionalObjects.length === 1) {
            return { score: 60, assessment: 'Moderately professional background' };
        } else {
            return { score: 30, assessment: 'Casual background' };
        }
    }

    assessProfessionalContext(labels) {
        const professionalLabels = ['Office', 'Business', 'Professional', 'Formal', 'Corporate'];
        const contextScore = labels
            .filter(label => professionalLabels.some(prof =>
                label.description.toLowerCase().includes(prof.toLowerCase())
            ))
            .reduce((sum, label) => sum + label.score, 0);

        return {
            score: Math.min(100, contextScore * 20), // Scale to 0-100
            assessment: contextScore > 2 ? 'Professional context' : 'Casual context'
        };
    }

    // Aggregate analysis across all frames
    aggregateFrameAnalysis(frameAnalyses) {
        const validFrames = frameAnalyses.filter(frame => frame.faces.detected);

        if (validFrames.length === 0) {
            return {
                faces: { detected: false },
                summary: {
                    presentationQuality: {
                        eyeContact: { score: 0, assessment: 'No face detected' },
                        enthusiasm: { score: 0, assessment: 'No face detected' },
                        background: { score: 0, assessment: 'No analysis possible' }
                    },
                    overallProfessionalScore: 0
                },
                textAnalysis: { professionalKeywords: [], hasCredentials: false },
                objectAnalysis: { professionalObjects: [] }
            };
        }

        // Aggregate face data
        const faceScores = validFrames.map(f => f.faces.professionalScore);
        const avgProfessionalScore = faceScores.reduce((a, b) => a + b, 0) / faceScores.length;

        const eyeContactScores = validFrames.map(f => f.faces.eyeContact.score);
        const avgEyeContact = eyeContactScores.reduce((a, b) => a + b, 0) / eyeContactScores.length;

        // Aggregate text data
        const allKeywords = validFrames.flatMap(f => f.text.professionalKeywords || []);
        const uniqueKeywords = [...new Set(allKeywords)];

        // Aggregate object data
        const backgroundScores = validFrames.map(f => f.objects.backgroundScore.score);
        const avgBackground = backgroundScores.reduce((a, b) => a + b, 0) / backgroundScores.length;

        return {
            faces: {
                detected: true,
                averageProfessionalScore: Math.round(avgProfessionalScore)
            },
            summary: {
                presentationQuality: {
                    eyeContact: {
                        score: Math.round(avgEyeContact),
                        assessment: this.getEyeContactAssessment(avgEyeContact)
                    },
                    enthusiasm: {
                        score: Math.round(avgProfessionalScore),
                        assessment: this.getEnthusiasmAssessment(avgProfessionalScore)
                    },
                    background: {
                        score: Math.round(avgBackground),
                        assessment: this.getBackgroundAssessment(avgBackground)
                    }
                },
                overallProfessionalScore: this.calculateOverallScore(avgEyeContact, avgProfessionalScore, avgBackground)
            },
            textAnalysis: {
                professionalKeywords: uniqueKeywords,
                hasCredentials: uniqueKeywords.length > 0
            },
            objectAnalysis: {
                professionalObjects: validFrames.flatMap(f => f.objects.professionalObjects || [])
            }
        };
    }

    getEyeContactAssessment(score) {
        if (score >= 80) return 'Excellent eye contact';
        else if (score >= 60) return 'Good eye contact';
        else if (score >= 40) return 'Moderate eye contact';
        else return 'Needs improvement';
    }

    getEnthusiasmAssessment(score) {
        if (score >= 70) return 'Very enthusiastic';
        else if (score >= 50) return 'Enthusiastic';
        else if (score >= 30) return 'Reserved';
        else return 'Very reserved';
    }

    getBackgroundAssessment(score) {
        if (score >= 80) return 'Professional setting';
        else if (score >= 60) return 'Moderately professional setting';
        else if (score >= 40) return 'Casual setting';
        else return 'Unprofessional setting';
    }

    calculateOverallScore(eyeContact, enthusiasm, background) {
        // Weighted average with eye contact being most important
        const weightedScore = (eyeContact * 0.4) + (enthusiasm * 0.3) + (background * 0.3);
        return Math.round(weightedScore);
    }

    getMockVisualAnalysis() {
        return {
            overallProfessionalScore: 85,
            summary: {
                presentationQuality: {
                    eyeContact: {
                        score: 80,
                        assessment: 'Good eye contact maintained throughout'
                    },
                    background: {
                        score: 75,
                        assessment: 'Professional home office setting'
                    },
                    enthusiasm: {
                        score: 90,
                        assessment: 'Very enthusiastic and energetic presentation'
                    }
                }
            },
            textAnalysis: {
                professionalKeywords: ['software developer', 'full-stack', 'Node.js', 'React', 'cloud technologies'],
                hasCredentials: true
            }
        };
    }
}

module.exports = new VisionAnalysisService();